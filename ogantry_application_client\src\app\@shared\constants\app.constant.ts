import Plugin from '../../../assets/plugins/formvalidation/src/js/core/Plugin';
/**
 * @sunflowerlab
 * <AUTHOR>
 */

export class AppConstants {
  public static readonly authenticationToken: string = 'authenticationToken';
  public static readonly refreshToken: string = 'refreshToken';
  public static readonly userKey: string = 'user';
  public static readonly format: string = 'yyyy-MM-dd';
  public static readonly dateFormate_YYYY_MM_DD = 'YYYY-MM-DD';
  public static readonly grossMargin: string = 'Gross Margin';
  public static readonly netMargin: string = 'Net Margin';
  public static readonly height: string = 'calc((var(--fixed-content-height, 1vh) * 100) - 120px)';
  public static readonly requiredMinLength: number = 3;
  public static readonly requiredMaxLength: number = 320;
  public static readonly passwordMaxLength: number = 100;
  public static readonly pointHoverOnGraph: number = 0.1;
  public static readonly pageSize: number = 10;
  public static readonly number: number = 1000;
  public static readonly minuteNumber: number = 60;
  public static readonly hoursNumber: number = 60;
  public static readonly dayNumber: number = 24;
  public static readonly secondNumber: number = 45;
  public static readonly MonthNumber: number = 30.416;
  public static readonly yearsNumber: number = 365;
  public static readonly duration: number = 2000;
  public static readonly _undoButtonDuration: number = 3000;
  public static readonly animateValue: number = 800;
  public static readonly setTimeOut: 300;
  public static readonly createPNLPlug = 'Adjustment Successfully Created';
  public static readonly editPNLPlug = 'Adjustment Successfully Updated';
  public static readonly shareFilter = 'Filter has been shared successfully';
  public static readonly unShareFilter = 'Filter has been unshared successfully';
  public static readonly deleteFilter = 'Filter has been deleted successfully';
  public static readonly updateFilter = 'Filter has been updated successfully';
  public static readonly updateClient = 'Client updated successfully';
  public static readonly updateEmployee = 'Employee updated successfully';
  public static readonly createGlobalDetail = 'Create global detail successfully';
  public static readonly updateGlobalDetail = 'Update global detail successfully';
  public static readonly copyProject = 'Project copied successfully';
  public static readonly timeEntryDataSave = 'Data Saved Successfully';
  public static readonly createExtendedField = 'Extended-Field created successfully';
  public static readonly updateExtendedField = 'Extended-Field updated successfully';
  public static readonly updatePosition = 'Position updated successfully';
  public static readonly defaultRole = 'Default Role';
  public static readonly filterLinkCopied = 'Copied sharable link to clipboard.';
  public static readonly problemFetchingFilterById = 'There was a problem fetching your filter.';
  public static readonly costOfGoodsSold = 'Cost of Goods Sold';
  public static readonly Underdevelopment = 'Under development';
  public static readonly somethingWentWrong = 'Something Went Wrong';
  public static readonly noDataFound = 'No Data Found';
  public static readonly DateFormat = 'MM/dd/YYYY';
  public static readonly UPDATE = 'Update ';
  public static readonly ManageTimesheetFileName = 'ManageTimeSheet';
  public static readonly COPY_PROJECT = 'copyProject';
  public static readonly regexForTwoDecimal = /^\d*\.?\d{0,2}$/;
  public static readonly regexForTwoDecimalValidation = /^\d*(\.\d{0,2})?$/;
  public static readonly regexForWhitespacePattern = /[\n\t\r\s]+/g;
  public static readonly regexForNonDigit = /[^\d]/g;
  public static readonly defaultTimePart = 'T00:00:00';
  public static readonly employeeErrorMessage = 'Employee not present at selected start date.';
  public static readonly expenseAddedSuccess = 'Expense added successfully';
  public static readonly statusMessage = {
    success: 'text-success',
    danger: 'text-danger'
  };
  public static readonly columnName = {
    monthly_Variance: 'monthly_Variance'
  };
  public static readonly paginationConfig = {
    rowsPerPageOptions: [10, 25, 50]
  };
  public static readonly DEFAULT_ROWS_PER_PAGE = 10;
  public static readonly DEFAULT_PAGE = 0;
  public static readonly MANAGE_SCREENS = {
    CLIENT: 'client',
    EMPLOYEE: 'employee',
    PROJECT: 'project',
    MANAGE_TIME: 'manageTime'
  };
  public static readonly numberWithCommasRegex = /\B(?=(\d{3})+(?!\d))/g;
  public static readonly nonNumericRegex = /[^0-9.-]+/g;
  public static readonly regexForDecimal = /\.(\d+)/;
  public static readonly PROJECT_VALIDATION = {
    ALREADY_IN_PROGRESS: 'Resume validation already in progress, skipping duplicate call',
    SUCCESSFUL: 'Resume validation successful:',
    ERROR: 'Error resuming validation:',
    COMPLETE: 'Resume validation complete'
  };
  public static readonly SESSION_STORAGE = {
    LEAVE_PROJECT_PAGE_CHOICE: 'leaveProjectPageChoice'
  };
  public static readonly SESSION_STORAGE_VALUES = {
    TRUE: 'true',
    FALSE: 'false'
  };
  public static readonly RECALCULATION_MESSAGES = [
    'Updating Projected Revenue',
    'Updating Projected Labor Expense',
    'Updating Projected Other Expense',
    'Updating Projected Margin',
    'Updating Projected Profit',
    'Updating Utilizations / Bench'
  ];
  public static readonly saveProjectFirstMessage = 'Please save the project first to adjust monthly retainer revenue.';
  public static readonly selectedMonthsLabel = '{0} months selected';
  public static readonly selectAtLeastOneMonth = 'Please select at least one month to update';
  public static readonly retainerPlugErrorMessage = 'Error updating retainer plugs';
  public static readonly retainerPlugSuccessMessage = 'Revenue updated for';
  public static readonly retainerPlugSuccessMessageSuffix = 'month(s)';
  public static readonly sgaPlugErrorMessage = 'Error updating S&GA plugs';
  public static readonly sgaPlugSuccessMessage = 'S&GA updated for';
  public static readonly sgaPlugSuccessMessageSuffix = 'month(s)';
  public static readonly MonthCalenderConfig = {
    monthName: ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'],
    calenderTitleMatch: /\d{4}/
  };
  public static readonly storageConst = {
    financialReviewFilter: 'financialReviewFilter'
  };
  public static readonly apiMessage = {
    requestINProgress: 'Processing your request... check back soon.',
    NoResultFound: 'No results found ',
    noDataFound: 'No'
  };
  public static readonly positionDataSave = 'Data Saved Successfully';
  public static readonly duplicatePosition = 'Position duplicated successfully';
  public static readonly duplicatePositionError = 'Failed to duplicate position';
  public static readonly addExpense = 'Expenses added successfully';
  public static readonly tooltipText = {
    total: 'Total',
    actualHours: 'Actual Hours',
    projectHours: 'Project Hours'
  };
  public static readonly financialReviewSetting = {
    projectExpense: 'project_expenses',
    workExceptions: 'Work Exceptions'
  };
  public static readonly csvRepairConst = {
    actualHours: 'Actual Hours',
    date: 'Date'
  };
  public static readonly regexForHyperlink = /^(http|https):\/\//i;
}
