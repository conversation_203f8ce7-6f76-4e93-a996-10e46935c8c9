import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { ManagePeopleService } from '@entities/manage-people/manage-people.service';
import { OGPositionList, OGProjectList } from '@entities/manage-people/mange-people.model';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import moment from 'moment';

enum SelectedDropdown {
  Employee = 'Employee',
  ClientName = 'Client Name',
  PositionName = 'Position Name',
  ProjectName = 'Project Name'
}

@Component({
  selector: 'app-repair-csv-data',
  templateUrl: './repair-csv-data.component.html',
  styleUrls: ['./repair-csv-data.component.scss']
})
export class RepairCsvDataComponent extends SflBaseComponent implements OnInit, OnChanges {
  updateAllButton: boolean = true;
  @Input() updatedCSVData;
  @Input() tableHeader;
  @Input() selectedColumns;
  @Input() OGClientList: string[];
  @Input() OGProjectList: OGProjectList[];
  @Input() OGPositionList: OGPositionList[];
  @Input() isAllDataMatched: boolean;
  @Input() formMapping: any;

  @Output() isDataIsChanged = new EventEmitter<boolean>();
  selectedPosition = '';
  dependedPosition = '';
  tableData: any;
  showincorrectDataToggle;
  showCorrectionModal = false;
  dropDownOptions = [];
  selectedCorrectData;
  selectedinCorrectData;
  selectedRowData;
  isDateCorrection: boolean;
  isHoursCorrection: boolean;
  correctedHours;
  constructor(private readonly managePeopleService: ManagePeopleService, private readonly cdf: ChangeDetectorRef) {
    super();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.hasOwnProperty('updatedCSVData') && changes.hasOwnProperty('tableHeader')) {
      this.tableData = this.updatedCSVData;
      if (changes.hasOwnProperty('isAllDataMatched')) {
        if (!this.isAllDataMatched) {
          this.showincorrectDataToggle = !this.isAllDataMatched;
          this.toggleIncoorectData();
        }
      }
    }
  }

  ngOnInit(): void {}

  toggleIncoorectData() {
    if (this.showincorrectDataToggle) {
      this.tableData = this.updatedCSVData.filter((res) => {
        const allMatched = Object.values(res).some((item: any) => item.isMatchWithOG === false);
        return allMatched;
      });
    } else {
      this.tableData = this.updatedCSVData;
    }
    if (this.showincorrectDataToggle && this.tableData.length === 0 && !this.isAllDataMatched) {
      this.showincorrectDataToggle = false;
      this.tableData = this.updatedCSVData;
    }
  }

  openCorrectionModal(event: any) {
    const data = event.data;
    const hasNotMatched = Object.entries(data)
      .filter(([key]) => key !== this.appConstants.csvRepairConst.date && key !== this.appConstants.csvRepairConst.actualHours)
      .some(([_, item]: [any, any]) => item.isMatchWithOG === false);

    if (event?.columnData?.header === this.appConstants.csvRepairConst.date && hasNotMatched) {
      return;
    }
    this.selectedRowData = event;
    this.selectedinCorrectData = event.data[event.columnData.columnField].value;

    const clientData = this.selectedRowData.data[this.selectedColumns.Client];
    const projectData = this.selectedRowData.data[this.selectedColumns.Project];
    const positionData = this.selectedRowData.data[this.selectedColumns.Position];
    this.selectedPosition = event.columnData.header as string;
    switch (event.columnData.header) {
      case 'Client': {
        this.dropDownOptions = this.OGClientList.map((res) => {
          return res;
        });
        this.isDateCorrection = false;
        this.isHoursCorrection = false;
        this.showCorrectionModal = true;
        break;
      }
      case 'Project': {
        if (clientData.isMatchWithOG) {
          this.setDropDownOptions(this.OGProjectList, 'projectName', 'clientName', clientData);
          this.isDateCorrection = false;
          this.isHoursCorrection = false;
          this.showCorrectionModal = true;
        }
        break;
      }
      case 'Position': {
        if (clientData.isMatchWithOG && projectData.isMatchWithOG) {
          this.setDropDownOptions(this.OGPositionList, 'positionName', 'projectName', projectData);
          this.isDateCorrection = false;
          this.isHoursCorrection = false;
          this.showCorrectionModal = true;
        }
        break;
      }
      case 'Employee': {
        if (clientData.isMatchWithOG && projectData.isMatchWithOG && positionData.isMatchWithOG) {
          this.setDropDownOptions(this.OGPositionList, 'employeeFullName', 'positionName', positionData);
          this.isDateCorrection = false;
          this.isHoursCorrection = false;
          this.showCorrectionModal = true;
        }
        break;
      }
      case 'Date': {
        this.isDateCorrection = true;
        this.isHoursCorrection = false;
        this.showCorrectionModal = true;
        break;
      }
      case 'Hours': {
        this.isHoursCorrection = true;
        this.isDateCorrection = false;
        this.showCorrectionModal = true;
        break;
      }
    }
  }

  setDropDownOptions(dataList, property, compareField, compareValue) {
    // Filtering the data based on the their parent value, and mapping the result to dropdown options.
    this.dropDownOptions = dataList
      .filter((res) => res[compareField] === compareValue.value)
      .map((res) => {
        return res[property];
      })
      .filter((res) => res !== 'null null' && res !== 'null');

    this.dropDownOptions = this.filterDuplicates(this.dropDownOptions);
  }

  filterDuplicates<T>(array: T[]): T[] {
    return array.filter((item, index) => array.indexOf(item) === index);
  }

  correctData(multiple: boolean) {
    if (multiple) {
      const deepCopySelectedRow = JSON.parse(JSON.stringify(this.selectedRowData));
      switch (this.selectedRowData.columnData.columnField) {
        case this.formMapping.Employee:
          {
            this.tableData = this.tableData.map((data) => {
              if (
                JSON.stringify(data[this.formMapping.Client]) === JSON.stringify(deepCopySelectedRow.data[this.formMapping.Client]) &&
                JSON.stringify(data[this.formMapping.Project]) === JSON.stringify(deepCopySelectedRow.data[this.formMapping.Project]) &&
                JSON.stringify(data[this.formMapping.Position].value) === JSON.stringify(deepCopySelectedRow.data[this.formMapping.Position].value) &&
                JSON.stringify(data[this.formMapping.Position].isMatchWithOG) === JSON.stringify(deepCopySelectedRow.data[this.formMapping.Position].isMatchWithOG) &&
                JSON.stringify(data[this.formMapping.Employee]) === JSON.stringify(deepCopySelectedRow.data[this.formMapping.Employee])
              ) {
                data[this.formMapping.Employee].value = this.selectedCorrectData;
                this.managePeopleService.isMatchWithOG(data, this.selectedColumns, this.OGClientList, this.OGProjectList, this.OGPositionList);
                return data;
              } else {
                return data;
              }
            });
          }
          break;
        case this.formMapping.Client:
          {
            this.tableData = this.tableData.map((data) => {
              if (JSON.stringify(data[this.formMapping.Client]) === JSON.stringify(deepCopySelectedRow.data[this.formMapping.Client])) {
                data[this.formMapping.Client].value = this.selectedCorrectData;
                this.managePeopleService.isMatchWithOG(data, this.selectedColumns, this.OGClientList, this.OGProjectList, this.OGPositionList);
                return data;
              } else {
                return data;
              }
            });
          }

          break;
        case this.formMapping.Position:
          {
            this.tableData = this.tableData.map((data) => {
              if (
                JSON.stringify(data[this.formMapping.Client]) === JSON.stringify(deepCopySelectedRow.data[this.formMapping.Client]) &&
                JSON.stringify(data[this.formMapping.Project]) === JSON.stringify(deepCopySelectedRow.data[this.formMapping.Project]) &&
                JSON.stringify(data[this.formMapping.Position].value) === JSON.stringify(deepCopySelectedRow.data[this.formMapping.Position].value) &&
                JSON.stringify(data[this.formMapping.Position].isMatchWithOG) === JSON.stringify(deepCopySelectedRow.data[this.formMapping.Position].isMatchWithOG)
              ) {
                data[this.formMapping.Position].value = this.selectedCorrectData;

                this.managePeopleService.isMatchWithOG(data, this.selectedColumns, this.OGClientList, this.OGProjectList, this.OGPositionList);
                return data;
              } else {
                return data;
              }
            });
          }

          break;
        case this.formMapping.Project:
          {
            this.tableData = this.tableData.map((data) => {
              if (
                JSON.stringify(data[this.formMapping.Client]) === JSON.stringify(deepCopySelectedRow.data[this.formMapping.Client]) &&
                JSON.stringify(data[this.formMapping.Project]) === JSON.stringify(deepCopySelectedRow.data[this.formMapping.Project])
              ) {
                data[this.formMapping.Project].value = this.selectedCorrectData;
                this.managePeopleService.isMatchWithOG(data, this.selectedColumns, this.OGClientList, this.OGProjectList, this.OGPositionList);
                return data;
              } else {
                return data;
              }
            });
          }

          break;
      }
    } else {
      if (this.selectedCorrectData) {
        const selectedField = this.selectedRowData.columnData.columnField;
        const cellValue = this.tableData[this.selectedRowData.index][selectedField];

        if (this.isDateCorrection) {
          cellValue.value = moment(this.selectedCorrectData).format('MM/DD/YYYY');
        } else if (this.isHoursCorrection) {
          const numericValue = +this.convertTimeToNumber(moment(this.selectedCorrectData).format('HH:mm')).toFixed(1);
          cellValue.value = numericValue;
        } else {
          cellValue.value = this.selectedCorrectData;
        }
      }
      this.managePeopleService.isMatchWithOG(this.selectedRowData.data, this.selectedColumns, this.OGClientList, this.OGProjectList, this.OGPositionList);
    }
    this.hideCorrectionModal();
    this.toggleIncoorectData();
    this.cdf.detectChanges();
    this.selectedCorrectData = null;
    this.isDataIsChanged.emit(true);
  }

  private convertTimeToNumber(timeString: string): number {
    const [hours, minutes] = timeString.split(':').map(Number);
    return +(hours + minutes / 60);
  }

  hideCorrectionModal() {
    this.showCorrectionModal = false;
  }
}
