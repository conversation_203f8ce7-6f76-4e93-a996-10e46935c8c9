import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { Total } from '../project.model';
import { ProjectService } from '@entities/project/project.service';
import { ProjectUserState } from '@shared/models/custom/purchase-order.model';
import { AppConstants } from '@shared/constants';
import { interval, Subscription } from 'rxjs';
@Component({
  selector: 'app-project-cost',
  templateUrl: './project-cost.component.html',
  styleUrls: ['./project-cost.component.scss']
})
export class ProjectCostComponent extends SflBaseComponent implements OnInit, OnDestroy, OnChanges {
  currentProjectStateValue = ProjectUserState;
  @Input() projectStatusLabel: ProjectUserState;
  @Input() currentProjectState: ProjectUserState;
  @Input() validationStatus: string;
  @Input() projectCost: Total;
  @Input() loading: boolean;
  @Input() recalculating: boolean;
  @Input() projectId: number;
  @Output() resumeServices: EventEmitter<boolean> = new EventEmitter();
  readonly validateState = ProjectUserState.Validated;

  private messageInterval: Subscription;
  private currentMessageIndex = 0;
  private originalProjectState: ProjectUserState;
  rotatingMessage: string = '';

  constructor(private readonly projectService: ProjectService, private readonly cdf: ChangeDetectorRef) {
    super();
  }

  ngOnInit(): void {
    this.watchForRecalculating();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.recalculating && !changes.recalculating.firstChange) {
      const recalculatingValue = changes.recalculating.currentValue;
      if (recalculatingValue) {
        this.startRotatingMessages();
      } else {
        this.stopRotatingMessages();
      }
    }
  }

  ngOnDestroy(): void {
    if (this.messageInterval) {
      this.messageInterval.unsubscribe();
    }
  }

  resumeRecalculating(): void {
    this.resumeServices.next(true);
  }

  private watchForRecalculating(): void {
    this.subscriptionManager.add(
      this.projectService.getRecalculatingTheRevenueSubject().subscribe((recalculating) => {
        if (recalculating) {
          this.startRotatingMessages();
        } else {
          this.stopRotatingMessages();
        }
      })
    );
  }

  private startRotatingMessages(): void {
    this.currentMessageIndex = 0;
    this.updateCurrentMessage();

    if (this.messageInterval) {
      this.messageInterval.unsubscribe();
    }

    this.messageInterval = interval(2000).subscribe(() => {
      this.currentMessageIndex = (this.currentMessageIndex + 1) % AppConstants.RECALCULATION_MESSAGES.length;
      this.updateCurrentMessage();
    });
  }

  private stopRotatingMessages(): void {
    if (this.messageInterval) {
      this.messageInterval.unsubscribe();
      this.messageInterval = null;
    }

    if (this.originalProjectState) {
      this.rotatingMessage = '';
    }
  }

  private updateCurrentMessage(): void {
    this.rotatingMessage = AppConstants.RECALCULATION_MESSAGES[this.currentMessageIndex];
  }
}
