<div>
  <!-- <app-card-header
    [cardTitle]="cardTitle"
    [cardSubTitle]="''"
    [buttons]="buttons"
    [cardLabelClass]="'mb-0'"
  ></app-card-header> -->
  <!-- {{ projectId }} {{ extendFields }} {{ extendFieldsObj }} -->
  <div>
    <div>
      <ng-container *ngTemplateOutlet="extendForm"></ng-container>
    </div>
    <!-- <div
      *ngIf="projectId && extendFields && extendFieldsObj"
      class="form-group d-flex flex-wrap justify-content-end align-items-center pt-5 mt-4"
    >
      <button
        id="ContactCancel"
        type="button"
        class="btn-cancel"
        (click)="onClose()"
      >
        Cancel
      </button>
      <button
        id="Submit"
        type="submit"
        [isSubmitting]="isSubmitting"
        class="btn-save"
        (click)="save()"
      >
        Save
      </button>
    </div> -->
  </div>
  <!-- <p *ngIf="!projectId" class="mt-5 text-capitalize text-center">No Data</p> -->
</div>
<ng-template #extendForm>
  <form #extendedFieldForm="ngForm">
    <div class="col-12 py-5">
      <ng-container *ngFor="let item of extendFields; let index = index">
        <!-- <p>{{ item.id }}</p> -->
        <ng-container *ngFor="let filed of item?.jsonData?.extendedFieldsConfig; let index2 = index">
          <ng-container *ngIf="filed?.component == componentType">
            <ng-container *ngFor="let filedDetails of filed?.fields; let index3 = index">
              <label *ngIf="checkShowORHide(filedDetails?.name)" class="form-label mt-3" [for]="filedDetails?.name">{{ filedDetails?.name }}</label>
              <ng-container *ngIf="checkShowORHide(filedDetails?.name) && filedDetails.type === filedType.Text">
                <input
                  type="text"
                  name="textInput_{{ filedDetails?.DBTag }}"
                  class="form-control custom"
                  required
                  [value]="getValueByPartialKey(filedDetails?.DBTag)"
                  (change)="updateObjectByPartialKey(filedDetails?.DBTag, $event.target.value)"
                  (focus)="updateObjectByPartialKey(filedDetails?.DBTag, $event.target.value)"
                  [placeholder]="filedDetails?.name"
                  [ngModel]="fieldValue"
                  #textInput="ngModel"
                />
                <div *ngIf="textInput.invalid && (textInput.dirty || textInput.touched) && isShowRequiredError">
                  <small class="text-danger" *ngIf="textInput.errors?.['required']">{{ filedDetails?.name }} is required.</small>
                </div>
              </ng-container>

              <ng-container *ngIf="checkShowORHide(filedDetails?.name) && filedDetails.type === filedType.Text_Area">
                <textarea
                  class="form-control custom"
                  name="textAreaInput_{{ filedDetails?.DBTag }}"
                  rows="5"
                  cols="5"
                  required
                  [value]="getValueByPartialKey(filedDetails?.DBTag)"
                  pInputTextarea
                  (change)="updateObjectByPartialKey(filedDetails?.DBTag, $event.target.value)"
                  (focus)="updateObjectByPartialKey(filedDetails?.DBTag, $event.target.value)"
                  [placeholder]="filedDetails?.name"
                  [ngModel]="fieldValue"
                  #textAreaInput="ngModel"
                ></textarea>
                <div *ngIf="textAreaInput.invalid && (textAreaInput.dirty || textAreaInput.touched) && isShowRequiredError" class="mb-2">
                  <small class="text-danger" *ngIf="textAreaInput.errors?.['required']">{{ filedDetails?.name }} is required.</small>
                </div>
              </ng-container>

              <ng-container *ngIf="checkShowORHide(filedDetails?.name) && filedDetails.type === filedType.Hyperlink">
                <input
                  type="text"
                  name="hyperlinkInput_{{ filedDetails?.DBTag }}"
                  class="form-control custom mb-2"
                  required
                  [value]="getValueByPartialKey(filedDetails?.DBTag)"
                  [placeholder]="filedDetails?.name"
                  [ngModel]="fieldValue"
                  #hyperlinkInput="ngModel"
                  (change)="updateObjectByPartialKey(filedDetails?.DBTag, $event.target.value)"
                  (focus)="updateObjectByPartialKey(filedDetails?.DBTag, $event.target.value)"
                />
                <div *ngIf="hyperlinkInput.invalid && (hyperlinkInput.dirty || hyperlinkInput.touched) && isShowRequiredError">
                  <small class="text-danger" *ngIf="hyperlinkInput.errors?.['required']">{{ filedDetails?.name }} is required.</small>
                </div>
              </ng-container>

              <ng-container *ngIf="checkShowORHide(filedDetails?.name) && filedDetails.type === filedType.Dropdown">
                <p-dropdown
                  name="dropdown_{{ filedDetails?.DBTag }}"
                  [options]="filedDetails?.options"
                  [ngModel]="getValueByPartialKey(filedDetails?.DBTag)"
                  (onChange)="updateObjectByPartialKey(filedDetails?.DBTag, $event.value)"
                  [placeholder]="filedDetails?.name"
                  class="extended-dropdown w-100"
                  appendTo="body"
                  optionLabel="name"
                  [showClear]="true"
                  required
                  #dropdown="ngModel"
                >
                </p-dropdown>
                <div *ngIf="dropdown.invalid && (dropdown.dirty || dropdown.touched) && isShowRequiredError" class="mb-2">
                  <small class="text-danger" *ngIf="dropdown.errors?.['required']">{{ filedDetails?.name }} is required.</small>
                </div>
              </ng-container>

              <ng-container *ngIf="checkShowORHide(filedDetails?.name) && filedDetails.type === filedType.MultiDropdown">
                <p-multiSelect
                  appendTo="body"
                  name="multiselect_{{ filedDetails?.DBTag }}"
                  [ngModel]="(extendFieldsObj?.[filedDetails?.DBTag] && Array.isArray(extendFieldsObj?.[filedDetails?.DBTag])) ? extendFieldsObj?.[filedDetails?.DBTag] : []"
                  [options]="filedDetails?.options"
                  (onChange)="updateObjectByPartialKey(filedDetails?.DBTag, $event.value, filedType.MultiDropdown)"
                  [placeholder]="filedDetails?.name"
                  class="extended-multiselect w-100"
                  display="chip"
                  optionLabel="name"
                  required
                  #multiSelect="ngModel"
                ></p-multiSelect>
                <div *ngIf="multiSelect.invalid && (multiSelect.dirty || multiSelect.touched) && isShowRequiredError" class="mb-2">
                  <small class="text-danger" *ngIf="multiSelect.errors?.['required']">{{ filedDetails?.name }} is required.</small>
                </div>
              </ng-container>
            </ng-container>
          </ng-container>
        </ng-container>
      </ng-container>
    </div>
  </form>
</ng-template>
