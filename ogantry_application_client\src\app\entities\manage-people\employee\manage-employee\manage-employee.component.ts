import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { ButtonParams } from '@shared/models';
import { MessageService, TableState } from 'primeng/api';
import { LayoutUtilsService } from '@shared/services/layout-utils.service';
import { Table } from 'primeng/table';
import { FormControl } from '@angular/forms';
import { AlertType } from '@shared/models/alert-type.enum';
import { ClientService } from '@entities/client/client.service';
import { DatePipe } from '@angular/common';
import { UtilizationService } from '@entities/utilization-management/utilization.service';
import { EmployeeType, Region } from '@entities/utilization-management/utilization.model';
import { Debounce } from '@shared/decorators/debounce.decorator';
import { GetSetCacheFiltersService } from '@shared/services/get-set-cache-filters.service';
import { AppConstants } from '@shared/constants';
import * as _ from 'lodash';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { ProjectService } from '@entities/project/project.service';
import {
  Employee,
  GlobalDetailSubCategory,
  GlobalDetailTaggingCategory,
  GlobalDetailTags,
  IFilter,
  ISavedFilterList,
  QueryFilter,
  QueryFilterParams,
  SaveFilter,
  SubCategory,
  TagCategory
} from '@entities/administration/administration.model';
import { TreeViewStructure, TreeNode, PNGTree } from '@entities/administration/append-tags/tree-view-model';
import { AppendTagsComponent } from '@entities/administration/append-tags/append-tags.component';
import { MatDialog } from '@angular/material/dialog';
import { ColumnToggleService } from '@shared/services/column-toggle.service';
import { AdministrationService } from '@entities/administration/administration.service';
import { Status } from '@shared/models/status.enum';
import { ComponentsType, FiledType } from '@shared/models/component-type-enum';
import { AuthNoticeService } from '@auth/auth-notice/auth-notice.service';
import { ExtendedFormComponent } from '@shared/components/extended-form/extended-form.component';
import { extendedField } from '@shared/models/extended-field.model';

@Component({
  selector: 'app-manage-employee',
  templateUrl: './manage-employee.component.html',
  styleUrls: ['./manage-employee.component.scss'],
  providers: [MessageService, DatePipe],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ManageEmployeeComponent extends SflBaseComponent implements OnInit, OnDestroy {
  @ViewChild('selectAllEmployee', { static: false })
  selectAllEmployee!: ElementRef<HTMLElement>;
  listSelected = false;
  employeeData: Employee[];
  employeeRoles = [];
  regions = [];
  employeeTypes = [];
  totalRecords = 20;
  loading = true;
  cardTitle = 'Manage Employees';
  cardSubTitle = null;
  endDate: Date;
  todayDate = new Date();
  buttons: ButtonParams[] = [
    // {
    //   btnSvg: "save",
    //   btnClass: "btn-filter-icon",
    //   action: () => this.onSave(),
    //   title: "Save Filter",
    // },
    // {
    //   btnSvg: "clear-filter",
    //   btnClass: "btn-filter-icon",
    //   action: () => this.resetFilters(),
    //   title: "Reset Filter",
    // },
    // {
    //   btnSvg: "filter-list",
    //   btnClass: "btn-filter-icon",
    //   action: () => this.showSavedFilterDropDown(),
    //   title: "Get Stored Filters",
    // },
    // {
    //   btnSvg: "filter",
    //   btnClass: "btn-filter-icon",
    //   action: () => this.showFilterOption(),
    //   title: "Show Filter",
    // },
    {
      btnClass: 'btn-save btn-add-wrapper',
      btnText: 'Add New',
      title: 'Add New Employee ',
      redirectPath: this.appRoutes.CREATE_EMPLOYEE,
      permissions: [this.permissionModules.MANAGE_EMPLOYEE]
    }
  ];
  showFilter = false;
  splitButtonDropDownOption = {
    action: this.showFilterOption.bind(this),
    options: [
      {
        label: 'Get Stored Filters',
        icon: 'get-stored-filter-split-button-icon',
        command: () => {
          this.showSavedFilterDropDown();
        }
      },
      {
        label: 'Save Filter',
        icon: 'save-filter-split-button-icon',
        command: () => {
          this.onSave();
        }
      },
      {
        label: 'Reset Filter',
        icon: 'reset-filter-split-button-icon',
        command: () => {
          this.resetFilters();
        }
      }
    ]
  };
  statuses = [];
  showPaginator: boolean;
  @ViewChild('dt') table: Table;
  dataFilter: IFilter = new IFilter();

  pageChangeFlag = false;
  sortColumnFlag = false;
  sortFieldName: string = 'first_name';
  sortOrderNumber: number = 1;
  availableFilters: ISavedFilterList;
  filteredFilters: ISavedFilterList;
  selectedFilter: QueryFilter;
  showSavedFilter = false;
  selectedFilterFormControl = new FormControl('');
  filteredFlag = false;
  inactive: boolean = false;
  activeEmp: Employee[];
  sharedFilters: QueryFilter[] = [];
  myFilters: QueryFilter[] = [];
  showEditDialog = false;
  editFilterObj: QueryFilter;
  showNameError = false;
  showDeleteDialog = false;
  deleteFilterObj: QueryFilter;
  showShareDialog = false;
  shareFilterObj = null;
  queryFilterId: number;
  allEmployeeTypes = [];
  showTagDialog = false;
  selectedTagToView: string;
  selectedTags = [];
  treeViewSelectedTags = [];
  finalTagsAlongWithTheCategory: string[] = [];
  groupedCategory: TreeViewStructure;
  globalDetailsTaggingCategory: GlobalDetailTaggingCategory;
  tagSubCategory: SubCategory[] = [];
  globalDetailsTagSubCategory: GlobalDetailSubCategory;
  globalDetailsTag: GlobalDetailTags;
  taggingTags = [];
  tagCategories: TagCategory[] = [];
  selectedCategoriesTag: PNGTree[] = [];
  checkedEmployee: Employee[] = [];
  isShowHideColumns: boolean = false;
  _selectedColumns: any;
  frozenCols: any[];
  _pCols: string[] = [];
  columnChangeHold: any;
  @Input() get selectedColumns(): any[] {
    return this._selectedColumns;
  }

  extendFields: any;
  componentType = ComponentsType.Employee;
  showUpdateExtendFiledDialog = false;
  employeeObj: Employee;
  filedType = FiledType;
  employeeId: number;
  employeeSetupForm: Employee;
  updateExtendFiled = '';
  extendFiledFilter: any = {};
  showHyperlinkNavigationDialog = false;
  linkValue = '';
  fieldDetail: extendedField;

  set selectedColumns(val: any) {
    setTimeout(() => {
      this.columnToggle.setSelectedColumns(this._selectedColumns, 'manageEmployee');
      const col = this._selectedColumns;
      if (col) {
        this._selectedColumns = col.filter((val) => val?.includes(val));
      } else {
        this._selectedColumns = this.frozenCols.filter((col) => val.includes(col));
      }
      this._pCols = col?.map((f) => f.field);
    }, 500);
  }
  @ViewChild('childForm') extendedFieldFormComponent: ExtendedFormComponent;

  constructor(
    private readonly adminService: AdministrationService,
    private readonly clientService: ClientService,
    private readonly cdf: ChangeDetectorRef,
    private readonly layoutUtilsService: LayoutUtilsService,
    private readonly datePipe: DatePipe,
    private readonly utilizationService: UtilizationService,
    private readonly cacheFilter: GetSetCacheFiltersService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly router: Router,
    private readonly projectService: ProjectService,
    private readonly dialog: MatDialog,
    private readonly columnToggle: ColumnToggleService
  ) {
    super();
  }

  ngOnInit(): void {
    this.checkEmployeeFilterStatus('employee');
    this.getStoredFilters();
    this.getGlobalDetailsCategory();
    this.getRegions();
    this.getEmployeeRoles();
    this.getEmployeeTypes();
    this.getCategoryMasterData();
    this.allEmployeeTypes = [
      { label: 'All Employees', value: 'all' },
      { label: 'Current Employees', value: 'active' },
      { label: 'Current & Future Employees', value: 'activefuture' },
      { label: 'Inactive Employees', value: 'inactive' },
      { label: 'Future Employees', value: 'future' }
    ];
    this.frozenCols = [
      { field: 'first_name', monthLabel: 'First Name', sort: true },
      { field: 'last_name', monthLabel: 'Last Name', sort: true },
      { field: 'email', monthLabel: 'Email', sort: true },
      { field: 'skill_set', monthLabel: 'Skill Set' },
      { field: 'start_date', monthLabel: 'Start Date', sort: true },
      { field: 'type', monthLabel: 'Type', sort: true },
      { field: 'cost', monthLabel: 'Cost', sort: true },
      { field: 'status', monthLabel: 'status', sort: true },
      { field: 'tags', monthLabel: 'Tags' }
    ];
    this._selectedColumns = JSON.parse(localStorage.getItem('selectedColumnsArray'))?.manageEmployee
      ? JSON.parse(localStorage.getItem('selectedColumnsArray'))['manageEmployee']
      : this.frozenCols;
    this._pCols = this._selectedColumns.map((f) => f.field);
    this.dataFilter.employee_status = 'activefuture';
    this.adminService.showNewAddedTags.subscribe((res: boolean) => {
      if (res) {
        this.getEmployeeList(this.table);
        this.checkedEmployee = [];
      }
    });
  }

  getRegions() {
    this.loading$.next(true);
    this.subscriptionManager.add(
      this.utilizationService.getRegions().subscribe(
        (res) => {
          if (res?.data?.regions) {
            this.regions.push({ label: '-- All --', value: '' });
            const regions = res?.data?.regions;
            regions.forEach((region: Region) => {
              this.regions.push({
                label: region?.region?.name,
                value: region?.region?.name
              });
            });
          }
          this.loading$.next(false);
        },
        () => this.loading$.next(false)
      )
    );
  }

  getEmployeeRoles() {
    this.loading$.next(true);
    this.subscriptionManager.add(
      this.utilizationService.getPositionTypes().subscribe(
        (res) => {
          if (res?.data?.position_types) {
            this.employeeRoles.push({ label: '-- All --', value: 'All' });
            res.data.position_types.forEach((type) => {
              this.employeeRoles.push({
                label: type.position_type.name,
                value: type.position_type.name
              });
            });
            if (this.cacheFilter.getCacheFilters(this.appConstants.MANAGE_SCREENS.EMPLOYEE)) {
              this.dataFilter = this.cacheFilter.getCacheFilters(this.appConstants.MANAGE_SCREENS.EMPLOYEE);
              if (this.dataFilter.start_date) {
                this.dataFilter.start_date = new Date(this.dataFilter.start_date);
              }
            }
          }
          this.loading$.next(false);
        },
        () => this.loading$.next(false)
      )
    );
  }

  getEmployeeTypes() {
    this.loading$.next(true);
    this.subscriptionManager.add(
      this.utilizationService.getEmployeeTypes().subscribe((res) => {
        if (res?.data?.employee_types) {
          this.employeeTypes.push({ label: '-- All --', value: '' });
          const regions = res?.data?.employee_types;
          regions.forEach((employeeType: EmployeeType) => {
            this.employeeTypes.push({
              label: employeeType?.employee_type?.name,
              value: employeeType?.employee_type?.name
            });
          });
        }
      })
    );
  }

  onSelectColumsChange(event?) {
    if (event) {
      this.columnToggle.setSelectedColumns(event.value, 'manageEmployee');
      this._selectedColumns = event.value;
      this._pCols = event.value.map((f) => f.field);
    }
    this.isShowHideColumns = !this.isShowHideColumns;
  }

  addTagsToMultipleProject() {
    const employeeId: Number[] = [];
    for (const employee of this.checkedEmployee) {
      employeeId.push(employee.employee.id);
    }
    const dialog = this.dialog.open(AppendTagsComponent, {
      data: {
        tags: [],
        employeeId: employeeId,
        title: 'Add Tag',
        flag: 'ADD TAGS TO CHECKED EMPLOYEE'
      },
      width: '880px'
    });

    dialog.afterClosed().subscribe((res) => {
      if (res) {
        this.listSelected = false;
        this.checkedEmployee = [];
        this.cdf.detectChanges();
      }
    });
  }

  clearFilter(key) {
    delete this.dataFilter[key];
    this.filter();
  }

  getStoredFilters() {
    const requestObject = {
      // is_shared: true,
      resource: 'employees'
    };
    this.subscriptionManager.add(
      this.clientService.getStoredFilters(requestObject).subscribe(
        (res: ISavedFilterList) => {
          this.sharedFilters = [];
          this.myFilters = [];
          this.loading = false;
          this.availableFilters = res;
          this.filteredFilters = JSON.parse(JSON.stringify(res)); // to deep copy the object
          this.sharedFilters = this.filteredFilters?.data?.query_filters?.filter((q) => q.query_filter.is_shared === true);
          this.myFilters = this.filteredFilters?.data?.query_filters?.filter((q) => q.query_filter.is_shared === false);
          this.cdf.detectChanges();
          this.routerListener();
        },
        () => (this.loading = false)
      )
    );
  }

  getCategoryMasterData() {
    this.tagCategories = [];
    this.loading = true;
    this.subscriptionManager.add(
      this.adminService.getTagCategories('TagCategoryManagement').subscribe(
        (res) => {
          this.loading = false;
          if (res?.data?.global_details) {
            const globalDetail = res?.data?.global_details;
            if (globalDetail && globalDetail[0]?.global_detail?.name === 'TagCategoryManagement') {
              this.globalDetailsTaggingCategory = globalDetail[0];
              this.tagCategories = globalDetail[0].global_detail.extended_fields.tagCategory;
              this.adminService.setTagCategories(globalDetail[0].global_detail);
              this.getTagSubCategories();
            }
          }
        },
        () => (this.loading = false)
      )
    );
  }

  getGlobalDetailTags() {
    this.taggingTags = [];
    this.loading = true;
    this.subscriptionManager.add(
      this.adminService.getTags('TagManagement').subscribe(
        (res) => {
          this.loading = false;
          if (res?.data?.global_details) {
            const globalDetail = res?.data?.global_details;
            if (globalDetail && globalDetail[0]?.global_detail?.name === 'TagManagement') {
              this.globalDetailsTag = globalDetail[0];
              this.taggingTags = globalDetail[0].global_detail.extended_fields.tags;
              this.adminService.setTags(globalDetail[0].global_detail);
              this.combineCategoryAndSubCategory();
            }
          }
        },
        () => (this.loading = false)
      )
    );
  }

  initGroupingCategoryTags() {
    this.groupedCategory = { data: [] };
    for (const [index, category] of this.globalDetailsTaggingCategory.global_detail.extended_fields.tagCategory.entries()) {
      const dataCollection: TreeNode = new TreeNode();
      dataCollection.label = category.name;
      dataCollection.selectable = false;
      dataCollection.collapsedIcon = 'pi-chevron-right';
      dataCollection.expandedIcon = 'pi-chevron-down';
      dataCollection.expanded = true;
      if (category?.subTagCategory?.length) {
        for (const [subIndex, subCate] of category?.subTagCategory?.entries()) {
          dataCollection.children.push({
            label: subCate.name,
            collapsedIcon: 'pi-chevron-right',
            expandedIcon: 'pi-chevron-down',
            children: [],
            selectable: false,
            expanded: true
          });
          if (subCate?.tags?.length) {
            for (const [tagIndex, tag] of subCate?.tags?.entries()) {
              dataCollection.children[subIndex]?.children?.push({
                label: tag.name,
                collapsedIcon: 'pi-chevron-right',
                expandedIcon: 'pi-chevron-down',
                expanded: true
              });
            }
          }
        }
      }
      if (category?.tags?.length) {
        for (const [tagIndex, tag] of category?.tags?.entries()) {
          dataCollection.children?.push({
            label: tag.name,
            collapsedIcon: 'pi-chevron-right',
            expandedIcon: 'pi-chevron-down',
            expanded: true
          });
        }
      }
      this.groupedCategory.data.push(dataCollection);
    }
    this.onFilterChangePreapareSelectedTreeNodes();
  }

  getTagSubCategories() {
    this.tagSubCategory = [];
    this.loading = true;
    this.subscriptionManager.add(
      this.adminService.getTagSubCategories('SubCategoryManagement').subscribe(
        (res) => {
          this.loading = false;
          if (res?.data?.global_details) {
            const globalDetail = res?.data?.global_details;
            if (globalDetail && globalDetail[0]?.global_detail?.name === 'SubCategoryManagement') {
              this.globalDetailsTagSubCategory = globalDetail[0];
              this.tagSubCategory = globalDetail[0].global_detail.extended_fields.subCategory;
              this.adminService.setTagSubCategories(globalDetail[0].global_detail);
              this.getGlobalDetailTags();
            }
          }
        },
        () => (this.loading = false)
      )
    );
  }

  combineCategoryAndSubCategory() {
    for (const category of this.globalDetailsTaggingCategory.global_detail.extended_fields.tagCategory) {
      category.subTagCategory = [...this.tagSubCategory?.filter((subCate) => subCate?.parentCategoryId === category?.id)];
    }
    this.injectTagsToRespectiveCategoryOrSubCategory();
    this.initGroupingCategoryTags();
  }

  injectTagsToRespectiveCategoryOrSubCategory() {
    for (const category of this.globalDetailsTaggingCategory.global_detail.extended_fields.tagCategory) {
      for (const tag of this.taggingTags) {
        if (tag.tagCategory === category.id) {
          const subCateIndex = category.subTagCategory.findIndex((subCate) => subCate.id === tag.subTagCategory);
          if (subCateIndex !== -1) {
            category.subTagCategory[subCateIndex]['tags'].push(tag);
          } else {
            category.tags.push(tag);
          }
        }
      }
      category.subTagCategory = [...this.tagSubCategory?.filter((subCate) => subCate?.parentCategoryId === category?.id)];
    }
  }

  tagSelected(event) {
    this.treeViewSelectedTags = [];
    this.dataFilter.tags = this.finalizedTags.length > 1 ? this.finalizedTags.split(',').toString() : this.finalizedTags.toString();
    this.filter();
  }

  get finalizedTags(): string {
    this.finalTagsAlongWithTheCategory = [];
    const categoryAndTags = this.selectedTags;
    this.selectedCategoriesTag = categoryAndTags.filter((tag) => !tag.hasOwnProperty('selectable'));
    for (const selectedTags of this.selectedCategoriesTag) {
      let labelHolder = '';
      labelHolder += selectedTags.label;
      if (selectedTags.parent) {
        labelHolder = selectedTags.parent.label + '__' + labelHolder;
        if (selectedTags.parent?.parent) {
          labelHolder = 'equals:' + selectedTags.parent?.parent?.label + '__' + labelHolder;
          if (selectedTags.parent?.parent?.parent) {
            labelHolder = selectedTags.parent?.parent?.parent?.label + '__' + labelHolder;
          }
        }
      }
      this.finalTagsAlongWithTheCategory.push(labelHolder);
    }
    return this.finalTagsAlongWithTheCategory.toString();
  }

  getExtractedTagsParentCategory(tagWithCategory: string): string {
    const tagArray = tagWithCategory.split('__');
    return tagArray[tagArray.length - 2];
  }

  onFilterChangePreapareSelectedTreeNodes() {
    if (this.dataFilter.tags?.length) {
      this.selectedTags = [];
      const tagsWithCategory = this.dataFilter.tags.split(',');
      for (const tag of tagsWithCategory) {
        const pngTreeItem: PNGTree = new PNGTree();
        const tagLength = tag.split('__');
        pngTreeItem.collapsedIcon = 'pi-chevron-right';
        pngTreeItem.expandedIcon = 'pi-chevron-down';
        pngTreeItem.label = this.getExtractedTags(tag);
        pngTreeItem.expanded = true;
        pngTreeItem.parent = {
          label: this.getExtractedTagsParentCategory(tag),
          children: [],
          collapsedIcon: 'pi-chevron-right',
          expandedIcon: 'pi-chevron-down',
          expanded: true,
          parent:
            tagLength?.length > 2
              ? {
                  label: this.getExtractedTagsParentCategory(tag),
                  children: [],
                  collapsedIcon: 'pi-chevron-right',
                  expandedIcon: 'pi-chevron-down',
                  expanded: true,
                  parent: undefined
                }
              : undefined
        };
        if (this.groupedCategory) {
          for (const parent of this.groupedCategory?.data) {
            for (const children of parent.children) {
              for (const children_data of children.children) {
                if (children_data.label === pngTreeItem.label) {
                  this.selectedTags.push(children_data);
                }
              }
            }
          }
        }
      }
      this.cdf.detectChanges();
    }
  }

  // used to apply filter on the table
  filter(): void {
    if (!this.dataFilter.position_types) {
      this.dataFilter.position_types = null;
    }
    if (!this.dataFilter.employee_type_name) {
      this.dataFilter.employee_type_name = null;
    }
    if (!this.dataFilter.region_name) {
      this.dataFilter.region_name = null;
    }
    this.filteredFlag = true;
    this.employeeData = [];
    this.loading = true;
    this.dataFilter.offset = this.appConstants.DEFAULT_PAGE;
    this.dataFilter.limit = this.table._rows;
    this.pageChangeFlag = false;
    this.getEmployeeList(this.table);
  }

  // used to convert and object into query param string
  applyFilter() {
    this.showSavedFilter = false;
    let filter = JSON.parse('{"' + decodeURIComponent(this.selectedFilter.query_filter.query_string).replace(/"/g, '\\"').replace(/&/g, '","').replace(/=/g, '":"') + '"}');
    if (filter.start_date) {
      filter.start_date = new Date(filter.start_date);
    }

    if (filter?.order_by) {
      filter.order_by = filter.order_by.replace(/%3A/g, ':');
    }
    if (this.dataFilter?.tags) {
      this.dataFilter.tags = this.dataFilter.tags.replace(/%3A/g, ':').replace(/%2C/g, ',');
    }
    this.dataFilter = filter;
    this.filter();
    this.selectedFilter = null;
    this.filteredFilters.data.query_filters = this.availableFilters.data.query_filters;
    this.onFilterChangePreapareSelectedTreeNodes();
  }
  styleObj = {
    heading: {
      color: 'rgb(109 108 108)',
      fontFamily: 'Poppins',
      fontSize: '10px',
      fontWeight: '500',
      letterSpacing: '0',
      lineHeight: '16px'
    },
    subHeading: {
      color: '#242424',
      fontFamily: 'Poppins',
      fontSize: '12px',
      letterSpacing: '0',
      lineHeight: '18px'
    }
  };

  showSavedFilterDropDown() {
    this.showSavedFilter = !this.showSavedFilter;
  }

  showFilterOption() {
    this.showFilter = !this.showFilter;
  }

  @Debounce()
  async getEmployeeList(event?: TableState) {
    let queryFilter: QueryFilterParams = {
      limit: this.pageChangeFlag ? (event?.rows ? event?.rows : this.dataFilter?.limit) : this.dataFilter?.limit ? this.dataFilter.limit : this.appConstants.DEFAULT_ROWS_PER_PAGE,
      offset: this.pageChangeFlag ? (event?.first ? event?.first : this.dataFilter?.offset) : this.dataFilter?.offset ? this.dataFilter.offset : this.appConstants.DEFAULT_PAGE,
      order_by: !this.sortColumnFlag ? (this.dataFilter?.order_by ? this.dataFilter.order_by : this.activeSort(event)) : this.activeSort(event)
    };

    if (this.dataFilter.employee_status === null) {
      this.dataFilter.employee_status = 'activefuture';
    }
    if (!this.pageChangeFlag) {
      this.table._first = this.dataFilter?.offset ? this.dataFilter.offset : event?.first;
      this.table._rows = this.dataFilter?.limit ? this.dataFilter.limit : event?.rows;
    }
    if (!this.sortColumnFlag) {
      this.sortFieldName = this.dataFilter?.order_by ? this.dataFilter.order_by.split(':')[1] : event?.sortField;
      this.sortOrderNumber = this.dataFilter?.order_by ? (this.dataFilter.order_by.split(':')[0] === 'asc' ? 1 : -1) : event?.sortOrder;
    }
    if (this.dataFilter) {
      this.cacheFilter.setCacheFilters({ ...this.dataFilter, ...queryFilter }, this.appConstants.MANAGE_SCREENS.EMPLOYEE);
      if (Object.entries(this.dataFilter).length) {
        if (this.filteredFlag) {
          queryFilter.offset = 0;
          event.first = 0;
          this.filteredFlag = false;
        }
        for (const [key] of Object.entries(this.dataFilter)) {
          if (key === 'first_name' || key === 'last_name' || key === 'email') {
            queryFilter[`${key}_search`] = this.dataFilter[key];
          } else if (key === 'start_date') {
            if (this.dataFilter[key]) {
              queryFilter[`${key}`] = this.datePipe.transform(new Date(this.dataFilter[key]), 'yyyy-MM-dd');
            } else {
              delete queryFilter[`${key}`];
            }
          } else if (key === 'position_types') {
            if (this.dataFilter[key] !== 'All') {
              queryFilter[`${key}`] = this.dataFilter[key];
            }
          } else {
            if (key !== 'offset' && key !== 'limit' && key !== 'order_by') queryFilter[`${key}`] = this.dataFilter[key];
          }
        }
      }
    }
    delete queryFilter['employee_active'];
    delete queryFilter['employee_inactive'];
    queryFilter = { ...queryFilter, ...this.extendFiledFilter };
    queryFilter = this.queryStringUtil(queryFilter);
    this.loading = true;
    this.employeeData = [];
    this.subscriptionManager.add(
      this.adminService.getEmployeeData(queryFilter).subscribe(
        (res) => {
          this.loading = false;
          this.employeeData = res.body.data.employees;
          this.employeeData?.map((employee) => {
            if (new Date().getTime() >= new Date(employee.employee.end_date).getTime()) {
              employee.employee.inactive = true;
            }
          });
          this.employeeData = this.employeeData?.map((emp) => {
            return {
              ...emp,
              status: this.empStatusUpdate(emp)
            };
          });
          this.totalRecords = Number(res.headers.get('x-total-count'));
          this.showPaginator = this.totalRecords <= 10 ? false : true;
          this.cdf.detectChanges();
        },
        () => (this.loading = false)
      )
    );
  }
  showActive(event) {
    this.dataFilter.employee_active = !this.dataFilter.employee_active;
    this.filter();
  }
  showInactive(event) {
    this.dataFilter.employee_inactive = !this.dataFilter.employee_inactive;
    this.filter();
  }

  getDate(event) {
    return this.datePipe.transform(event, 'MM/dd/yyyy');
  }

  employeeStatusChange(event) {
    this.dataFilter.employee_status = event.target.defaultValue;
    this.filter();
  }

  getSkillSet(positionTypes) {
    const positionType = [];
    if (positionTypes.length) {
      positionTypes.forEach((type) => {
        positionType.push(' ' + type?.position_type?.name);
      });
      return positionType.join();
    }
    return '-';
  }

  // used to take query string param and removes values which are not set in the query string
  queryStringUtil(queryStringParam: QueryFilterParams) {
    const queryFilter: QueryFilterParams = {};
    if (queryStringParam) {
      for (const [key] of Object.entries(queryStringParam)) {
        if (queryStringParam[key] === '' || queryStringParam[key] === null || queryStringParam[key] === undefined) {
          delete queryStringParam[key];
        }
      }
    }
    return queryStringParam;
  }

  activeSort(event?: TableState) {
    if (event?.sortField) {
      if (event.sortOrder === 1) {
        return 'asc:' + event.sortField;
      } else {
        return 'desc:' + event.sortField;
      }
    }
    return null;
  }

  getTagCategorySubCategory(tags: string): string {
    const tagArray = tags.split('__');
    const categoryName = tagArray[0];
    const subCategory = tagArray?.length > 2 ? tagArray[1] : null;
    if (subCategory) return `Category <strong>${categoryName}</strong> <br> Sub Category<strong>${subCategory}</strong>`;
    else return `Category <strong>${categoryName}</strong>`;
  }

  toggleWithCategory(tooltip, tag) {
    if (tooltip.isOpen()) {
      tooltip.close();
    } else {
      tooltip.open({ tag });
    }
  }

  openTagModal(tag) {
    this.showTagDialog = true;
    this.selectedTagToView = tag;
  }

  getTagsCount(tagWithCategory: string, allowedTagLength?: boolean): string {
    if (allowedTagLength) {
      const tagArray = tagWithCategory.split('__');
      return tagArray[tagArray.length - 1];
    }
  }

  getTagCount(tags: string[]): string {
    return '+ ' + (tags?.length - 2);
  }

  getExtractedTags(tagWithCategory: string): string {
    const tagArray = tagWithCategory.split('__');
    return tagArray[tagArray.length - 1];
  }

  // saving the filter, prompting the user to provide the filter group name.
  onSave() {
    const queryFilter = this.queryStringUtil(this.dataFilter);
    const requestObject: SaveFilter = {
      // is_shared: true,
      query_string: this.serialize(queryFilter),
      resource: 'employees'
    };
    const dialogTitle = 'Save Employee Filter';
    const dialogRef = this.layoutUtilsService.saveClientGroupName(dialogTitle, requestObject, 'Name');
    dialogRef.afterClosed().subscribe((filtersResponse) => {
      if (filtersResponse) {
        this.getStoredFilters();
        this.layoutUtilsService.showActionNotification('Filter has been saved successfully', AlertType.Success);
      }
    });
  }

  // converts the object in to query param string
  serialize = (obj) => {
    const str = [];
    for (const p in obj) {
      if (obj.hasOwnProperty(p)) {
        str.push(encodeURIComponent(p) + '=' + encodeURIComponent(obj[p]));
      }
    }
    return str.join('&');
  };

  searchFilters(filter: string) {
    this.filteredFilters.data.query_filters = filter?.length
      ? this.availableFilters?.data?.query_filters?.filter((availableFilter) => availableFilter.query_filter.name.toLowerCase().includes(filter.toLowerCase()))
      : this.availableFilters?.data?.query_filters;
    this.sharedFilters = this.filteredFilters?.data?.query_filters?.filter((q) => q.query_filter.is_shared === true);
    this.myFilters = this.filteredFilters?.data?.query_filters?.filter((q) => q.query_filter.is_shared === false);
  }

  pageChange() {
    this.loading = true;
    this.employeeData = [];
    this.pageChangeFlag = true;
    this.updateEmployeeFilterStatus(this.appConstants.MANAGE_SCREENS.EMPLOYEE);
  }

  sortColumn() {
    this.sortColumnFlag = true;
  }

  resetFilters(): void {
    this.cacheFilter.resetCacheFilters(this.appConstants.MANAGE_SCREENS.EMPLOYEE);
    // reseting the url by removing the filter query string
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: { filterId: null },
      queryParamsHandling: 'merge'
    });
    // reset the filter selection as well
    this.selectedFilterFormControl = new FormControl('');
    this.dataFilter = new IFilter();
    this.dataFilter.employee_status = 'activefuture';
    this.dataFilter.tags = '';
    this.selectedTags = [];
    this.pageChangeFlag = false;
    this.dataFilter.limit = this.appConstants.DEFAULT_ROWS_PER_PAGE;
    this.dataFilter.offset = this.appConstants.DEFAULT_PAGE;

    if (this.table) {
      this.table.first = this.appConstants.DEFAULT_PAGE; // Reset to the first page
      this.table.rows = this.appConstants.DEFAULT_ROWS_PER_PAGE; // Default rows per page
    }
    this.filter();
  }

  clearStartDate() {
    delete this.dataFilter.start_date;
    this.filter();
  }

  editFilter(filter) {
    this.showEditDialog = true;
    this.editFilterObj = _.cloneDeep(filter);
  }

  closeModal() {
    this.showEditDialog = false;
    this.editFilterObj = null;
    this.showNameError = false;
    this.showDeleteDialog = false;
    this.deleteFilterObj = null;
    this.showShareDialog = false;
    this.shareFilterObj = null;
  }

  saveEditFilter() {
    if (!this.editFilterObj.query_filter.name.length) {
      this.showNameError = true;
    } else {
      this.isSubmitting = true;
      this.subscriptionManager.add(
        this.clientService.updateFilter(this.editFilterObj.query_filter.id, this.editFilterObj).subscribe(
          (res) => {
            this.layoutUtilsService.showActionNotification(AppConstants.updateFilter, AlertType.Success);
            this.isSubmitting = false;
            this.closeModal();
            this.getStoredFilters();
            this.showSavedFilter = true;
            this.cdf.detectChanges();
          },
          () => (this.isSubmitting = false)
        )
      );
    }
  }

  inputFilterName() {
    this.showNameError = false;
  }

  deleteFilter(filterOption) {
    this.showDeleteDialog = true;
    this.deleteFilterObj = filterOption;
  }

  saveDeleteFilter() {
    this.isSubmitting = true;
    this.subscriptionManager.add(
      this.clientService.deleteStoredFilters(this.deleteFilterObj.query_filter.id).subscribe(
        (res) => {
          this.layoutUtilsService.showActionNotification(AppConstants.deleteFilter, AlertType.Success);
          this.isSubmitting = false;
          this.closeModal();
          this.getStoredFilters();
          this.showSavedFilter = true;
          this.cdf.detectChanges();
          this.utilizationService.showNewSharedFilter.next('Manage Employees');
        },
        () => (this.isSubmitting = false)
      )
    );
  }

  shareFilter(filterOption) {
    this.showShareDialog = true;
    this.shareFilterObj = {
      ...filterOption,
      header: 'Share',
      text: `Do you want to share Filter "${filterOption.query_filter.name}" to all users publically?`
    };
    this.shareFilterObj.query_filter.is_shared = !this.shareFilterObj.query_filter.is_shared;
  }

  unShareFilter(filterOption) {
    this.showShareDialog = true;
    this.shareFilterObj = {
      ...filterOption,
      header: 'Unshare',
      text: `Do you want to unshare Filter "${filterOption.query_filter.name}" from all users?`
    };
    this.shareFilterObj.query_filter.is_shared = !this.shareFilterObj.query_filter.is_shared;
  }

  saveShareFilter() {
    this.isSubmitting = true;
    this.subscriptionManager.add(
      this.clientService.updateFilter(this.shareFilterObj.query_filter.id, this.shareFilterObj).subscribe(
        (res) => {
          this.layoutUtilsService.showActionNotification(this.shareFilterObj.query_filter.is_shared ? AppConstants.shareFilter : AppConstants.unShareFilter, AlertType.Success);
          this.isSubmitting = false;
          this.closeModal();
          this.getStoredFilters();
          this.showSavedFilter = true;
          this.cdf.detectChanges();
          this.utilizationService.showNewSharedFilter.next('Manage Employees');
        },
        () => (this.isSubmitting = false)
      )
    );
  }

  private routerListener() {
    this.activatedRoute.queryParamMap.subscribe((params: Params) => {
      // grab the filter id from the url if present we will make an api call to get the specific filter from that id.
      if (params.params.filterId) {
        this.queryFilterId = params.params.filterId;
        this.getTheFilterById();
      } else if (this.cacheFilter.getCacheFilters(this.appConstants.MANAGE_SCREENS.EMPLOYEE)) {
        this.dataFilter = this.cacheFilter.getCacheFilters(this.appConstants.MANAGE_SCREENS.EMPLOYEE);
      }
    });
  }

  // used to get the filter by its id
  getTheFilterById() {
    this.subscriptionManager.add(
      this.projectService.getTheFilterById(this.queryFilterId).subscribe(
        (res) => {
          // if we get some response only then we may try to apply filter
          if (res) {
            this.selectedFilter = res?.data;
            const filterValue = this.sharedFilters?.find((f) => JSON.stringify(f) === JSON.stringify(this.selectedFilter));
            if (filterValue) {
              this.selectedFilterFormControl.setValue(filterValue);
            }
            this.applyFilter();
          }
        },
        (error) => {
          this.layoutUtilsService.showActionNotification(AppConstants.problemFetchingFilterById, AlertType.Error);
        }
      )
    );
  }

  copyLinkToTheFilter(filterId: number) {
    const filterHolder = document.createElement('textarea');
    filterHolder.style.position = 'fixed';
    filterHolder.style.left = '0';
    filterHolder.style.top = '0';
    filterHolder.style.opacity = '0';
    // construct the full url to be copied
    // e.g. http://localhost:4200/project/manage?filterId=3
    const hostName = `${window.location.protocol}${window.location.host}`;
    const filterString = `${hostName}${this.router.url.split('?')[0]}/?filterId=${filterId}`;

    filterHolder.value = filterString;
    document.body.appendChild(filterHolder);
    filterHolder.focus();
    filterHolder.select();
    document.execCommand('copy');
    document.body.removeChild(filterHolder);
    this.layoutUtilsService.showActionNotification(AppConstants.filterLinkCopied, AlertType.Success);
  }

  applySelectedFilterAndUpdateUrl() {
    this.showSavedFilter = false;
    this.selectedFilter = this.selectedFilterFormControl.value;
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: { filterId: this.selectedFilter?.query_filter?.id },
      queryParamsHandling: 'merge'
    });
  }

  viewStaffedPosition(employee) {
    const employeeFilter = {
      employee_ids: employee?.employee?.id?.toString(),
      employeeName: [employee?.employee?.first_name + employee?.employee?.last_name],
      filterFromMangeEmployee: true
    };
    this.cacheFilter.setCacheFilters(employeeFilter, 'positions-report');
    this.router.navigateByUrl(this.appRoutes.VIEW_STAFFED_POSITION);
  }

  listChecked(): void {
    this.checkedEmployee.length ? (this.listSelected = true) : (this.listSelected = false);
  }

  removeEmployee(): void {
    this.checkedEmployee = [];
  }

  selectAllEmployeeCheck(): void {
    this.checkedEmployee = this.employeeData;
  }

  empStatusUpdate(emp: Employee): string {
    const currentDate = new Date();
    const startDateParts = emp.employee.start_date.split('-');
    const endDateParts = emp.employee.end_date.split('-');

    // Constructing Date objects from parsed components
    const startDate = new Date(parseInt(startDateParts[0]), parseInt(startDateParts[1]) - 1, parseInt(startDateParts[2]));
    const endDate = new Date(parseInt(endDateParts[0]), parseInt(endDateParts[1]) - 1, parseInt(endDateParts[2]));

    if (endDate < currentDate) return Status.inactive;
    if (startDate > currentDate) return Status.future;
    return Status.active;
  }

  updateEmployeeFilterStatus(page: string): void {
    const filter = this.cacheFilter.getCacheFilters(page);
    this.cacheFilter.setCacheFilters({ ...filter, ...{ filterOpen: this.showFilter } }, page);
  }

  checkEmployeeFilterStatus(page: string): void {
    const filter = this.cacheFilter.getCacheFilters(page);
    if (filter?.filterOpen) {
      this.showFilter = filter.filterOpen;
    }
  }

  redirectToMangeTime(employee: any): void {
    this.router.navigate([`${this.appRoutes.VIEW_Manage_time}`], {
      queryParams: {
        email: employee.employee.email
      }
    });
  }

  getValueByPartialKey(dbTag: string, extendFieldsObj: any = {}, fieldType: string = ''): string {
    if (fieldType === this.filedType.MultiDropdown && extendFieldsObj?.hasOwnProperty(dbTag)) {
      const value = Array.isArray(extendFieldsObj[dbTag]) ? extendFieldsObj[dbTag]?.map((item) => item?.name).join(', ') : '';
      return value;
    }
    if (fieldType === this.filedType.Dropdown && extendFieldsObj?.hasOwnProperty(dbTag)) {
      return extendFieldsObj?.hasOwnProperty(dbTag) ? extendFieldsObj[dbTag].name : '';
    }

    return extendFieldsObj?.hasOwnProperty(dbTag) ? extendFieldsObj[dbTag] : '';
  }

  getGlobalDetailsCategory(): void {
    this.subscriptionManager.add(
      this.adminService.getExtendedFields('ManageExtendedFiled').subscribe(
        (res) => {
          this.loading = false;
          if (res?.data?.global_details) {
            const globalDetail = res?.data?.global_details;
            if (globalDetail && globalDetail[0]?.global_detail?.name === 'ManageExtendedFiled') {
              this.extendFields = res?.data?.global_details[0]?.global_detail?.extended_fields?.extendArray || [];

              this.frozenCols = [...this.frozenCols, ...this.extractExtendFlow()];
            }
          }
        },
        () => (this.loading = false)
      )
    );
  }

  checkSelectedColumn(key: string): boolean {
    const finalKey = key?.replace(/\s/g, '_')?.trim() || '';
    return this._pCols.includes(finalKey);
  }

  extractNames(data: any, componentName: string): string[] {
    return data
      ?.map((item) => {
        const projectConfig = item?.jsonData?.extendedFieldsConfig?.find((config) => config?.component === componentName);
        return projectConfig ? projectConfig?.fields.map((field) => field?.name?.trim()) : [];
      })
      .flat(2);
  }

  extractExtendFlow(): Array<any> {
    let extendFiled = [];
    let prepareKey = this.extractNames(this.extendFields, this.componentType);
    for (const key of prepareKey) {
      extendFiled.push({ field: key?.replace(/\s/g, '_')?.trim() || '', monthLabel: key?.toUpperCase() });
    }
    return extendFiled;
  }

  openExtendFiledPopup(employeeObj: Employee, fieldDetail: extendedField, isEditLink = false): void {
    this.fieldDetail = fieldDetail;
    this.linkValue = this.getValueByPartialKey(fieldDetail?.DBTag, employeeObj?.employee?.extended_fields, fieldDetail?.type) || '';
    if (fieldDetail?.type === this.filedType.Hyperlink && this.linkValue && !isEditLink) {
      this.openHyperlinkPopup(employeeObj, fieldDetail?.name);
      return;
    }
    this.updateExtendFiled = fieldDetail?.name;
    this.showUpdateExtendFiledDialog = true;
    this.employeeObj = employeeObj;
    this.employeeId = employeeObj?.employee?.id;
    this.employeeSetupForm = JSON.parse(JSON.stringify(this.employeeObj));
    this.showHyperlinkNavigationDialog = false;
  }

  openHyperlinkPopup(employeeObj: Employee, fieldName?: string): void {
    this.showHyperlinkNavigationDialog = true;
    this.updateExtendFiled = fieldName;
    this.employeeObj = employeeObj;
  }

  closeExtendFiledPopup(): void {
    this.employeeData = this.employeeData?.map((employee) => {
      if (employee?.employee?.id === this.employeeId) {
        return {
          employee: {
            ...employee.employee,
            extended_fields: { ...this.employeeSetupForm.employee.extended_fields }
          }
        };
      }
      return employee;
    });

    this.updateExtendFiled = '';
    this.showUpdateExtendFiledDialog = false;
    this.employeeObj = {} as Employee;
    this.fieldDetail = null;
    this.extendedFieldFormComponent?.onReset();
  }

  getUpdatedObj(employee: Employee) {
    const updatedEmployee = {
      extended_fields: employee?.employee?.extended_fields
    };
    return updatedEmployee;
  }

  onSaveExtendedEdit(): void {
    if (JSON.stringify(this.employeeSetupForm?.employee) !== JSON.stringify(this.employeeObj?.employee)) {
      this.isSubmitting = true;
      this.loading = true;
      const employeeObj = this.getUpdatedObj(this.employeeObj);
      this.subscriptionManager.add(
        this.adminService.updateEmployee(employeeObj, this.employeeId).subscribe(
          (res) => {
            this.showUpdateExtendFiledDialog = false;
            this.getEmployeeList(this.table);
            this.layoutUtilsService.showActionNotification(this.appConstants.updateEmployee, AlertType.Success);
            this.loading = false;
            this.isSubmitting = false;
            this.extendedFieldFormComponent?.onReset();
          },
          (err) => {
            this.loading = false;
            this.isSubmitting = false;
            this.extendedFieldFormComponent?.onReset();
            this.layoutUtilsService.showActionNotification(err.errors, AlertType.Error);
          }
        )
      );
    }
    this.closeExtendFiledPopup();
  }

  filterExtendFiled(dbTag: string, event: any): void {
    if (event?.value) {
      this.extendFiledFilter[dbTag] = `${event?.value?.trim()}` as string;
    } else {
      this.deleteExtendFiledFilter(dbTag);
    }
    this.filter();
  }
  deleteExtendFiledFilter(dbTag): void {
    if (this.extendFiledFilter?.hasOwnProperty(dbTag)) {
      delete this.extendFiledFilter[dbTag];
    }
    this.filter();
  }

  getFilterValue(dbTag: string): string {
    return this.extendFiledFilter?.hasOwnProperty(dbTag) ? this.extendFiledFilter[dbTag] : ('' as string);
  }

  isEmployeeFutureFinancial(startDate: Date | string): boolean {
    return new Date(startDate) > new Date();
  }

  getValidLink(link: string): string {
    if (!link) return '';
    return this.appConstants.regexForHyperlink.test(link) ? link : `https://${link}`;
  }

  checkExtendedFormValidity(): boolean {
    const isValid = this.extendedFieldFormComponent?.isFormValid() || false;
    return !isValid;
  }

  navigateOnLink(): void {
    this.showHyperlinkNavigationDialog = false;
    this.fieldDetail = null;
    if (this.linkValue) {
      const hyperLink = this.getValidLink(this.linkValue);
      window.open(hyperLink, '_blank');
      this.linkValue = '';
    }
  }

  ngOnDestroy(): void {
    this.updateEmployeeFilterStatus(this.appConstants.MANAGE_SCREENS.EMPLOYEE);
  }
}
