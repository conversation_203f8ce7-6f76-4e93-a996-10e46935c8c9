Stack trace:
Frame         Function      Args
0000005FF350  00021006118E (0002102B5B12, 000210272B3E, 00000000005E, 0000005FAEB0) msys-2.0.dll+0x2118E
0000005FF350  0002100469BA (000000000000, 000000000000, 000000000134, 000000001000) msys-2.0.dll+0x69BA
0000005FF350  0002100469F2 (000000000000, 00000000005A, 00000000005E, 000000000000) msys-2.0.dll+0x69F2
0000005FF350  0002101792D8 (0002102B5892, 000800000000, 00080000DFB8, 000000000000) msys-2.0.dll+0x1392D8
0000005FF350  000210183C77 (000000000000, 000210228268, 000210228250, 0000005FD320) msys-2.0.dll+0x143C77
0000005FF350  000210046DF4 (00021031C800, 0000005FD320, 000000000000, 000000000000) msys-2.0.dll+0x6DF4
0000005FF350  00021004850F (00007FFE0384, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x850F
0000005FF350  00021007251C (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x3251C
0000005FF5F0  7FFFDB7C9A1D (000210040000, 000000000001, 000000000000, 0000005FF538) ntdll.dll+0x19A1D
0000005FF5F0  7FFFDB81D2F7 (7FFFDB7EDB00, 000000763901, 7FFF00000001, 000000000001) ntdll.dll+0x6D2F7
0000005FF5F0  7FFFDB81D08A (0000007639A0, 0000005FF5F0, 000000770930, 000000070000) ntdll.dll+0x6D08A
0000005FF5F0  7FFFDB81D110 (7FFFDB8D5910, 000000000000, 000000000010, 0000005FF690) ntdll.dll+0x6D110
000000000000  7FFFDB883CB2 (000000000000, 000000000000, 000000000001, 000000000000) ntdll.dll+0xD3CB2
000000000000  7FFFDB825DEB (7FFFDB7B0000, 000000000000, 00000039C000, 000000000000) ntdll.dll+0x75DEB
000000000000  7FFFDB825C73 (000000000000, 000000000000, 000000000000, 000000000000) ntdll.dll+0x75C73
000000000000  7FFFDB825C1E (000000000000, 000000000000, 000000000000, 000000000000) ntdll.dll+0x75C1E
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFFDB7B0000 ntdll.dll
7FFFDB4C0000 KERNEL32.DLL
7FFFD9270000 KERNELBASE.dll
7FFFD2310000 apphelp.dll
7FFFDA4B0000 USER32.dll
7FFFD97A0000 win32u.dll
7FFFDAAB0000 GDI32.dll
7FFFD9680000 gdi32full.dll
7FFFD9020000 msvcp_win.dll
000210040000 msys-2.0.dll
7FFFD8E40000 ucrtbase.dll
7FFFD9C10000 advapi32.dll
7FFFDB5F0000 msvcrt.dll
7FFFDAAE0000 sechost.dll
7FFFDB2C0000 RPCRT4.dll
7FFFD8F40000 bcrypt.dll
7FFFD87E0000 CRYPTBASE.DLL
7FFFD95F0000 bcryptPrimitives.dll
