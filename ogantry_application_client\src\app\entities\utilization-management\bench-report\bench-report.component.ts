import { FormControl } from '@angular/forms';
import { Employees, QueryFilter } from './../../project/project.model';
import { OGantryHttpResponse } from './../../../@shared/models/custom/http-response.model';
import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, HostListener, Input, OnInit, ViewChild } from '@angular/core';
import { MatSidenav } from '@angular/material/sidenav';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { ButtonParams } from '@shared/models';
import { SidebarParams } from '@shared/models/sidebar-params.model';
import {
  EmployeeData,
  Group,
  EmployeeType,
  FilterReport,
  IFilter,
  MONTH_NAMES,
  QueryFilterParams,
  TableHeader,
  CalendarPageViewConfigType,
  ResorceType
} from '../utilization.model';
import { Table } from 'primeng/table';
import { SortEvent } from 'primeng/api';
import { DatePipe } from '@angular/common';
import { UtilizationService } from '../utilization.service';
import { ProjectService } from '@entities/project/project.service';
import { LayoutConfigService } from '@shared/services/layout-config.service';
import { AppConstants } from '@shared/constants';
const height = 'calc((var(--fixed-content-height, 1vh) * 100) - 180px)';
import { ISavedFilterList, SaveFilter } from '@entities/administration/administration.model';
import { AlertType } from '@shared/models/alert-type.enum';
import { LayoutUtilsService } from '@shared/services/layout-utils.service';
import { GetSetCacheFiltersService } from '@shared/services/get-set-cache-filters.service';
import { BehaviorSubject } from 'rxjs';
import moment from 'moment';
import { Project, Employee } from '@entities/project/project.model';
import { NgbPopover } from '@ng-bootstrap/ng-bootstrap';
import { MultiSelect } from 'primeng/multiselect';
import * as _ from 'lodash';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { ClientService } from '@entities/client/client.service';
import { GlobalDetailSubCategory, GlobalDetailTaggingCategory, GlobalDetailTags, SubCategory, TagCategory } from '@entities/administration/administration.model';
import { PNGTree, TreeNode, TreeViewStructure } from '@entities/administration/append-tags/tree-view-model';
import { AdministrationService } from '@entities/administration/administration.service';
import { MbscEventcalendarOptions, MbscEventcalendarView, MbscPopup, MbscPopupOptions } from '@mobiscroll/angular';
import { ColumnToggleService } from '@shared/services/column-toggle.service';
import { DialogService } from 'primeng/dynamicdialog';

@Component({
  selector: 'app-bench-report',
  templateUrl: './bench-report.component.html',
  styleUrls: ['./bench-report.component.scss'],
  providers: [DatePipe, DialogService]
})
export class BenchReportComponent extends SflBaseComponent implements OnInit, AfterViewInit {
  cardTitle = 'Utilization Report';
  utilizationEmployeeName = '';
  utilizationDetails = false;
  openFilter = false;
  updatingButton = false;
  isTableView: string = 'table';
  tableButtonActive = true;
  calendarButtonActive = false;
  preparingCalendarData = true;
  timelineType: CalendarPageViewConfigType = CalendarPageViewConfigType.year;
  timelineResolution: CalendarPageViewConfigType = CalendarPageViewConfigType.month;
  myEvents = [];
  myResources = [];
  loadingReport: boolean;
  greenColor = '#4b99a2';
  selectedPosition;
  @ViewChild('popup', { static: false })
  calenderPopup!: MbscPopup;
  popupOptions: MbscPopupOptions = {
    display: 'center'
  };
  calendarOptions: MbscEventcalendarOptions = {
    resources: this.myResources,
    onEventHoverIn: (args, inst) => {},
    onEventClick: (args, inst) => {
      const event: any = args.event;
      const resource: EmployeeData = this.benchReportData.find((data) => data.employee?.id === event.resource);
      this.bindPositionToSelectedPosition(resource, args.event);
      this.cdf.detectChanges();
      this.calenderPopup.open();
    }
  };
  myView: MbscEventcalendarView = {
    timeline: {
      type: this.timelineType,
      size: 1,
      resolution: this.timelineResolution,
      eventList: true
    }
  };
  cardSubTitle = null;
  buttons: ButtonParams[] = [];

  exportButtons: ButtonParams[] = [
    {
      action: this.exportReport.bind(this)
    }
  ];

  filterCardTitle = 'Apply Filter';
  filterButtons: ButtonParams[] = [
    {
      btnSvg: 'filter-list',
      btnClass: 'btn-filter-icon',
      action: this.openSaveFilterList.bind(this)
    },
    {
      btnSvg: 'save',
      btnClass: 'btn btn-sm btn-icon btn-icon-light svg-icon svg-icon-md icon-background mr-2 filter-btn-wrapper',
      action: this.onSaveFilter.bind(this)
    },
    {
      btnClass: 'btn-close-icon',
      btnIcon: 'times',
      action: this.onClose.bind(this)
    }
  ];
  splitButtonDropDownOption = {
    action: this.openSideBar.bind(this),
    options: [
      {
        label: 'Get Stored Filters',
        icon: 'get-stored-filter-split-button-icon',
        command: () => {
          this.openSaveFilterList();
        }
      },
      {
        label: 'Save Filter',
        icon: 'save-filter-split-button-icon',
        command: () => {
          this.onSaveFilter();
        }
      },
      {
        label: 'Reset Filter',
        icon: 'reset-filter-split-button-icon',
        command: () => {
          this.resetFilter();
        }
      }
    ]
  };

  sidebarParams: SidebarParams<FilterReport>;
  @ViewChild('sidebarFilter', { static: true }) el: MatSidenav;
  @ViewChild('popOver') public popOver: NgbPopover;
  @ViewChild('benchReportTable') table: Table;
  csvCols = [];
  exportPdfColumns = [];
  loading = false;
  dataFilter: IFilter = new IFilter();
  benchReportData: EmployeeData[] = [];
  tableHeaders: TableHeader[] = [];
  sorting = true;
  employeeTypes: EmployeeType[];
  employeeGroup: Group[];
  exportReportData = [];
  excelExportReportData = [];
  excelHeaders = [];
  tags = [];
  helpLoader: boolean;
  dateError = false;
  dateRequired = false;
  statuses = [];
  defaultStatuses: string[];
  frozenCols = [];
  projectStatus: any;
  styleObj = {
    heading: {
      color: 'rgb(109 108 108)',
      fontFamily: 'Poppins',
      fontSize: '10px',
      fontWeight: '500',
      letterSpacing: '0',
      lineHeight: '16px'
    },
    subHeading: {
      color: '#242424',
      fontFamily: 'Poppins',
      fontSize: '12px',
      letterSpacing: '0',
      lineHeight: '18px'
    }
  };
  height = height;
  resizeFlag = false;
  showFilterListDialog = false;
  availableFilters = null;
  selectedFilter = null;
  sortColumnFlag = false;
  sortFieldName: string;
  sortOrderNumber: number = 1;
  loading$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  activeEmployee = [];
  loadingEmp = false;
  @ViewChild('multiSelectComp') multiSelectComp: MultiSelect;
  @ViewChild('multiSelectComp2') multiSelectComp2: MultiSelect;
  employeeList: Employee[] = [];
  showEmployeeFilter = false;
  employeeName;
  selectedEmployeeName = [];
  sharedFilters: QueryFilter[] = [];
  myFilters: QueryFilter[] = [];
  showSavedFilter = false;
  selectedFilterFormControl = new FormControl('');
  editFilterObj: QueryFilter;
  showNameError = false;
  showDeleteDialog = false;
  deleteFilterObj: QueryFilter;
  showShareDialog = false;
  shareFilterObj = null;
  showEditDialog = false;
  filteredFilters: ISavedFilterList;
  queryFilterId: number;
  employeeStatusList = [];
  _selectedColumns: any;
  _pCols: string[] = [];
  isShowHideColumns: boolean = false;
  showTagDialog = false;
  selectedTagToView: string;
  viewUtilizationFormates = [];
  selectedUtiliuzation: string;
  allocationOptions = [];
  defaultView: string = 'table';
  tagCategories: TagCategory[] = [];
  taggingTags = [];
  globalDetailsTaggingCategory: GlobalDetailTaggingCategory;
  tagSubCategory: SubCategory[] = [];
  globalDetailsTagSubCategory: GlobalDetailSubCategory;
  globalDetailsTag: GlobalDetailTags;
  groupedCategory: TreeViewStructure;
  selectedTags = [];
  selectedCategoriesTag: PNGTree[] = [];
  treeViewSelectedTags = [];
  finalTagsAlongWithTheCategory: string[] = [];
  showApplyMsg = true;
  showExportOptionDialog: boolean;
  showExportOptions: boolean;

  @Input() get selectedColumns(): any[] {
    return this._selectedColumns;
  }

  set selectedColumns(val) {
    setTimeout(() => {
      this.columnToggle.setSelectedColumns(this._selectedColumns, 'benchReport');
      const col = this._selectedColumns;
      this._pCols = col?.map((f) => f.field);
    }, 500);
  }

  constructor(
    private readonly datePipe: DatePipe,
    private readonly utilizationService: UtilizationService,
    private readonly cdf: ChangeDetectorRef,
    private readonly projectService: ProjectService,
    private readonly layoutConfigService: LayoutConfigService,
    private readonly layoutUtilsService: LayoutUtilsService,
    private readonly cacheFilter: GetSetCacheFiltersService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly clientService: ClientService,
    private readonly router: Router,
    private readonly adminService: AdministrationService,
    private readonly columnToggle: ColumnToggleService
  ) {
    super();
  }

  ngOnInit(): void {
    this.loading$.next(true);
    this.dataFilter.employeeName = [];
    this.dataFilter.selectedAllocationType = [];
    this.dataFilter.selectedoverAllocationValue = 100;
    this.dataFilter.selectedunderAllocationValue = 80;

    if (window.innerWidth <= 1024) {
      this.resizeFlag = true;
    } else {
      this.resizeFlag = false;
    }
    this.frozenCols = [
      {
        field: 'first_name',
        monthLabel: 'Name',
        sort: true,
        isFrozenColumn: true
      },
      {
        field: 'skill_set',
        monthLabel: 'Skill Set',
        sort: false,
        isFrozenColumn: false,
        cssClass: 'skill-set-wrapper'
      },
      {
        field: 'employee_type_name',
        monthLabel: 'Type',
        sort: true,
        isFrozenColumn: false,
        cssClass: 'employee-type-wrapper'
      },
      {
        field: 'tags',
        monthLabel: 'Tags',
        sort: false,
        isFrozenColumn: false,
        cssClass: 'tags-wrapper'
      }
    ];

    this._selectedColumns = JSON.parse(localStorage.getItem('selectedColumnsArray'))?.benchReport
      ? JSON.parse(localStorage.getItem('selectedColumnsArray'))['benchReport']
      : this.frozenCols;
    this._pCols = this._selectedColumns.map((f) => f.field);

    this.dataFilter.employee_status = '';
    if (this.cacheFilter.getCacheFilters('bench-report')) {
      this.dataFilter = this.cacheFilter.getCacheFilters('bench-report');
    }
    this.employeeStatusList = [
      { label: 'All Employees', value: 'all' },
      { label: 'Current Employees', value: 'active' },
      { label: 'Current & Future Employees', value: 'activefuture' },
      { label: 'Inactive Employees', value: 'inactive' }
    ];
    this.viewUtilizationFormates = [
      { label: 'Hours', value: 'Hours' },
      { label: 'Percent', value: 'Percent' },
      { label: 'Dollars', value: 'Dollars' }
    ];
    this.allocationOptions = [
      { label: 'Over Allocated', value: 'Over Allocated' },
      { label: 'Under Allocated', value: 'Under Allocated' }
    ];
    this.clientService.saveFilterData$.subscribe((res) => {
      res === true ? this.filterReport() : '';
    });

    if (this.dataFilter.filterView === 'table') {
      this.defaultView = 'table';
    } else if (this.dataFilter.filterView === 'calendar') {
      this.defaultView = 'calendar';
    }
    if (!this.selectedUtiliuzation) {
      this.selectedUtiliuzation = this.dataFilter.selectedUtilizationToView;
    } else {
      this.dataFilter.selectedUtilizationToView = this.selectedUtiliuzation;
    }

    this.buttons = [
      {
        btnSvg: 'download-wt',
        btnClass: 'btn-filter-icon download',
        action: this.openExportOptionList.bind(this)
      },
      {
        btnSvg: this.tableButtonActive ? 'table-active' : 'table',
        btnClass: 'btn-switcher btn-left',
        isActive: this.tableButtonActive,
        isSwitcherButton: true,
        viewType: 'table',
        action: this.switchToCalendarView.bind(this)
      },
      {
        btnSvg: this.calendarButtonActive ? 'calendar-active' : 'calendar',
        btnClass: 'btn-switcher btn-right',
        isActive: this.calendarButtonActive,
        isSwitcherButton: true,
        viewType: 'calendar',
        action: this.switchToTableView.bind(this)
      }
    ];
    this.openFilter = true;
    this.getProjectStatus();
    this.setExportHeaders();
    this.getEmployeeTypes();
    this.getEmployeeGroup();
    this.getEmployeeList();
    this.getStoredFilters();
    this.applyAllFilter();
    this.getCategoryMasterData();
  }

  @HostListener('window:resize', ['$event'])
  onResize(event) {
    if (event.target.innerWidth <= 1024) {
      this.resizeFlag = true;
    } else {
      this.resizeFlag = false;
    }
  }

  sortColumn() {
    this.sortColumnFlag = true;
  }

  showHideColumns(type) {
    if (type == 'showColumns') {
      this.isShowHideColumns = true;
    } else {
      this.isShowHideColumns = false;
    }
  }

  onSelectColumsChange(event) {
    if (event) {
      this.columnToggle.setSelectedColumns(event.value, 'benchReport');
      this._selectedColumns = event.value;
      this._pCols = event.value.map((f) => f.field);
    }
    this.isShowHideColumns = !this.isShowHideColumns;
  }

  getTagCategorySubCategory(tags: string): string {
    const tagArray = tags.split('__');
    const categoryName = tagArray[0];
    const subCategory = tagArray?.length > 2 ? tagArray[1] : null;
    if (subCategory) return `Category <strong>${categoryName}</strong> <br> Sub Category<strong>${subCategory}</strong>`;
    else return `Category <strong>${categoryName}</strong>`;
  }

  toggleWithCategory(tooltip, tag) {
    if (tooltip.isOpen()) {
      tooltip.close();
    } else {
      tooltip.open({ tag });
    }
  }

  openTagModal(tag) {
    this.showTagDialog = true;
    this.selectedTagToView = tag;
  }

  getTagsCount(tagWithCategory: string, allowedTagLength?: boolean): string {
    if (allowedTagLength) {
      const tagArray = tagWithCategory.split('__');
      return tagArray[tagArray.length - 1];
    }
  }

  getTagCount(tags: string[]): string {
    return '+ ' + (tags?.length - 2);
  }

  getExtractedTags(tagWithCategory: string): string {
    const tagArray = tagWithCategory.split('__');
    return tagArray[tagArray.length - 1];
  }

  getExtractedTagsFromSelectedTags(tags: string[]) {
    return tags.map((tag) => tag.split('__').pop() || '');
  }

  setExportHeaders() {
    this.exportPdfColumns = [
      { title: 'Name', dataKey: 'name' },
      { title: 'Skill Set', dataKey: 'skill_set' },
      { title: 'Type', dataKey: 'employee_type' },
      { title: 'Tags', dataKey: 'tags' }
    ];

    this.csvCols = this.exportPdfColumns.map((col) => col.dataKey);
    this.excelHeaders = [
      {
        name: 'Name',
        skill_set: 'Skill Set',
        employee_type: 'Type',
        tags: 'tags'
      }
    ];
  }

  openExportOptionList() {
    this.showExportOptionDialog = true;
    this.showExportOptions = true;
    this.cdf.detectChanges();
  }

  get finalizedTags(): string {
    this.finalTagsAlongWithTheCategory = [];
    const categoryAndTags = this.selectedTags;
    this.selectedCategoriesTag = categoryAndTags.filter((tag) => !tag.hasOwnProperty('selectable'));
    for (const selectedTags of this.selectedCategoriesTag) {
      let labelHolder = '';
      labelHolder += selectedTags.label;
      if (selectedTags.parent) {
        labelHolder = selectedTags.parent.label + '__' + labelHolder;
        if (selectedTags.parent?.parent) {
          labelHolder = 'equals:' + selectedTags.parent?.parent?.label + '__' + labelHolder;
          if (selectedTags.parent?.parent?.parent) {
            labelHolder = selectedTags.parent?.parent?.parent?.label + '__' + labelHolder;
          }
        }
      }
      this.finalTagsAlongWithTheCategory.push(labelHolder);
    }
    return this.finalTagsAlongWithTheCategory.toString();
  }

  getCategoryMasterData() {
    this.tagCategories = [];
    this.loading = true;
    this.subscriptionManager.add(
      this.adminService.getTagCategories('TagCategoryManagement').subscribe(
        (res) => {
          this.loading = false;
          if (res?.data?.global_details) {
            const globalDetail = res?.data?.global_details;
            if (globalDetail && globalDetail[0]?.global_detail?.name === 'TagCategoryManagement') {
              this.globalDetailsTaggingCategory = globalDetail[0];
              this.tagCategories = globalDetail[0].global_detail.extended_fields.tagCategory;
              this.adminService.setTagCategories(globalDetail[0].global_detail);
              this.getTagSubCategories();
            }
          }
        },
        () => (this.loading = false)
      )
    );
  }

  getTagSubCategories() {
    this.tagSubCategory = [];
    this.loading = true;
    this.subscriptionManager.add(
      this.adminService.getTagSubCategories('SubCategoryManagement').subscribe(
        (res) => {
          this.loading = false;
          if (res?.data?.global_details) {
            const globalDetail = res?.data?.global_details;
            if (globalDetail && globalDetail[0]?.global_detail?.name === 'SubCategoryManagement') {
              this.globalDetailsTagSubCategory = globalDetail[0];
              this.tagSubCategory = globalDetail[0].global_detail.extended_fields.subCategory;
              this.adminService.setTagSubCategories(globalDetail[0].global_detail);
              this.getGlobalDetailTags();
            }
          }
        },
        () => (this.loading = false)
      )
    );
  }

  getGlobalDetailTags() {
    this.taggingTags = [];
    this.loading = true;
    this.subscriptionManager.add(
      this.adminService.getTags('TagManagement').subscribe(
        (res) => {
          this.loading = false;
          if (res?.data?.global_details) {
            const globalDetail = res?.data?.global_details;
            if (globalDetail && globalDetail[0]?.global_detail?.name === 'TagManagement') {
              this.globalDetailsTag = globalDetail[0];
              this.taggingTags = globalDetail[0].global_detail.extended_fields.tags;
              this.adminService.setTags(globalDetail[0].global_detail);
              this.combineCategoryAndSubCategory();
            }
          }
        },
        () => (this.loading = false)
      )
    );
  }

  combineCategoryAndSubCategory() {
    for (const category of this.globalDetailsTaggingCategory.global_detail.extended_fields.tagCategory) {
      category.subTagCategory = [...this.tagSubCategory?.filter((subCate) => subCate?.parentCategoryId === category?.id)];
    }
    this.injectTagsToRespectiveCategoryOrSubCategory();
    this.initGroupingCategoryTags();
  }

  injectTagsToRespectiveCategoryOrSubCategory() {
    for (const category of this.globalDetailsTaggingCategory.global_detail.extended_fields.tagCategory) {
      for (const tag of this.taggingTags) {
        if (tag.tagCategory === category.id) {
          const subCateIndex = category.subTagCategory.findIndex((subCate) => subCate.id === tag.subTagCategory);
          if (subCateIndex !== -1) {
            category.subTagCategory[subCateIndex]['tags'].push(tag);
          } else {
            category.tags.push(tag);
          }
        }
      }
      category.subTagCategory = [...this.tagSubCategory?.filter((subCate) => subCate?.parentCategoryId === category?.id)];
    }
  }

  initGroupingCategoryTags() {
    this.groupedCategory = { data: [] };
    for (const [index, category] of this.globalDetailsTaggingCategory.global_detail.extended_fields.tagCategory.entries()) {
      const dataCollection: TreeNode = new TreeNode();
      dataCollection.label = category.name;
      dataCollection.selectable = false;
      dataCollection.collapsedIcon = 'pi-chevron-right';
      dataCollection.expandedIcon = 'pi-chevron-down';
      dataCollection.expanded = true;
      if (category?.subTagCategory?.length) {
        for (const [subIndex, subCate] of category?.subTagCategory?.entries()) {
          dataCollection.children.push({
            label: subCate.name,
            collapsedIcon: 'pi-chevron-right',
            expandedIcon: 'pi-chevron-down',
            children: [],
            selectable: false,
            expanded: true
          });
          if (subCate?.tags?.length) {
            for (const [tagIndex, tag] of subCate?.tags?.entries()) {
              dataCollection.children[subIndex]?.children?.push({
                label: tag.name,
                collapsedIcon: 'pi-chevron-right',
                expandedIcon: 'pi-chevron-down',
                expanded: true
              });
            }
          }
        }
      }
      if (category?.tags?.length) {
        for (const [tagIndex, tag] of category?.tags?.entries()) {
          dataCollection.children?.push({
            label: tag.name,
            collapsedIcon: 'pi-chevron-right',
            expandedIcon: 'pi-chevron-down',
            expanded: true
          });
        }
      }
      this.groupedCategory.data.push(dataCollection);
    }
    const data = document.getElementById('project-status');
    data.click();
    this.onFilterChangePreapareSelectedTreeNodes();
  }

  tagSelected(event) {
    this.dataFilter.tags = this.finalizedTags;
    this.treeViewSelectedTags = [];
  }

  onFilterChangePreapareSelectedTreeNodes() {
    if (this.dataFilter.tags?.length) {
      this.selectedTags = [];
      const tagsWithCategory = this.dataFilter.tags.split(',');
      for (const tag of tagsWithCategory) {
        const pngTreeItem: PNGTree = new PNGTree();
        const tagLength = tag.split('__');
        pngTreeItem.collapsedIcon = 'pi-chevron-right';
        pngTreeItem.expandedIcon = 'pi-chevron-down';
        pngTreeItem.label = this.getExtractedTags(tag);
        pngTreeItem.expanded = true;
        pngTreeItem.parent = {
          label: this.getExtractedTagsParentCategory(tag),
          children: [],
          collapsedIcon: 'pi-chevron-right',
          expandedIcon: 'pi-chevron-down',
          expanded: true,
          parent:
            tagLength?.length > 2
              ? {
                  label: this.getExtractedTagsParentCategory(tag),
                  children: [],
                  collapsedIcon: 'pi-chevron-right',
                  expandedIcon: 'pi-chevron-down',
                  expanded: true,
                  parent: undefined
                }
              : undefined
        };
        if (this.groupedCategory) {
          for (const parent of this.groupedCategory.data) {
            for (const children of parent.children) {
              for (const children_data of children.children) {
                if (children_data.label === pngTreeItem.label) {
                  this.selectedTags.push(children_data);
                }
              }
            }
          }
        }
      }
      this.cdf.detectChanges();
    }
  }

  getExtractedTagsParentCategory(tagWithCategory: string): string {
    const tagArray = tagWithCategory.split('__');
    return tagArray[tagArray.length - 2];
  }

  getSkillSet(positionTypes) {
    const positionType = [];
    if (positionTypes.length) {
      positionTypes.forEach((type) => {
        positionType.push(type?.position_type?.name);
      });
      return positionType.join(', ');
    }
    return '-';
  }

  getEmployeeTypes() {
    this.subscriptionManager.add(
      this.utilizationService.getEmployeeTypes().subscribe((res) => {
        this.employeeTypes = res.data.employee_types;
        this.cdf.detectChanges();
      })
    );
  }

  getEmployeeGroup() {
    const requestObject = {
      // is_shared: true,
      resource: 'employees'
    };

    this.subscriptionManager.add(
      this.utilizationService.getGroup(requestObject).subscribe((res) => {
        const response = JSON.parse(JSON.stringify(res));
        const employeeGrp = [];
        response?.data?.query_filters?.map((query) => {
          employeeGrp.push({
            label: query.query_filter.name,
            value: {
              name: query.query_filter.name,
              value: query.query_filter.query_string
            }
          });
        });
        this.employeeGroup = employeeGrp;
        this.sortList(this.employeeGroup);
        this.cdf.detectChanges();
      })
    );
  }

  getEmployeeList() {
    this.employeeList = [];
    this.employeeName = [];
    const queryFilter = {
      order_by: 'asc:first_name',
      employee_status: this.dataFilter.employee_status
    };

    this.subscriptionManager.add(
      this.projectService.getEmployeeList(queryFilter).subscribe((res: OGantryHttpResponse<Employees>) => {
        res?.data?.employees?.forEach((e) => {
          this.employeeList.push({
            label: e.employee?.name,
            value: e.employee.id.toString()
          });
        });

        this.sortList(this.employeeList);
        if (this.dataFilter.value) {
          this.dataFilter.value = this.dataFilter.value;
          this.cdf.detectChanges();
        }
      })
    );
  }

  sortList(sortList) {
    if (this.sortList.length > 0) {
      sortList.sort((a, b) => {
        const fa = a?.label?.toLowerCase();
        const fb = b?.label?.toLowerCase();
        if (fa < fb) {
          return -1;
        }
        if (fa > fb) {
          return 1;
        }
        return 0;
      });
    }
  }

  customSort(event: SortEvent) {
    this.sorting = false;
    let filter = this.cacheFilter.getCacheFilters('bench-report');
    if (event?.field) {
      if (event?.order === 1) {
        filter.order_by = 'asc:' + event.field;
      } else {
        filter.order_by = 'desc:' + event.field;
      }
    }

    this.cacheFilter.setCacheFilters({ ...filter }, 'bench-report');

    event.data.sort((data1, data2) => {
      let value1 = null;
      let value2 = null;
      if (event.field === 'first_name') {
        value1 = data1.employee[event.field];
        value2 = data2.employee[event.field];
      } else if (event.field === 'employee_type_name') {
        value1 = data1.employee.employee_type.name;
        value2 = data2.employee.employee_type.name;
      } else if (this.selectedUtiliuzation === 'Dollars') {
        const dataValue1 = data1.employee.utilizations.filter((e) => e.utilization.hasOwnProperty(event.field));
        const dataValue2 = data2.employee.utilizations.filter((e) => e.utilization.hasOwnProperty(event.field));

        if (dataValue1.length) {
          value1 = parseFloat(dataValue1[0].utilization['display_utilization'].replace(/[$,]/g, ''));
        }

        if (dataValue2.length) {
          value2 = parseFloat(dataValue2[0].utilization['display_utilization'].replace(/[$,]/g, ''));
        }
      } else if (this.selectedUtiliuzation === 'Hours') {
        const dataValue1 = data1.employee.utilizations.filter((e) => e.utilization.hasOwnProperty(event.field));
        const dataValue2 = data2.employee.utilizations.filter((e) => e.utilization.hasOwnProperty(event.field));
        if (dataValue1.length) {
          value1 = +dataValue1[0].utilization['display_utilization'];
        }
        if (dataValue2.length) {
          value2 = +dataValue2[0].utilization['display_utilization'];
        }
      } else {
        const dataValue1 = data1.employee.utilizations.filter((e) => e.utilization.hasOwnProperty(event.field));
        const dataValue2 = data2.employee.utilizations.filter((e) => e.utilization.hasOwnProperty(event.field));
        if (dataValue1.length) {
          value1 = dataValue1[0].utilization[event.field];
        }
        if (dataValue2.length) {
          value2 = dataValue2[0].utilization[event.field];
        }
        if (value1 === value2) {
          value1 = data1.employee['first_name'];
          value2 = data2.employee['first_name'];
        }
      }

      let result = null;
      if (value1 == null && value2 != null) {
        result = -1;
      } else if (value1 != null && value2 == null) {
        result = 1;
      } else if (value1 == null && value2 == null) {
        result = 0;
      } else if (typeof value1 === 'string' && typeof value2 === 'string') {
        result = value1.localeCompare(value2);
      } else {
        result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;
      }
      return event.order * result;
    });
  }
  async loadReport(event?: SortEvent) {
    this.loading = true;
    // this.sidebarParams.template.close();
    this.openFilter = false;
    this.showApplyMsg = false;
    this.loadingReport = true;
    this.benchReportData = [];
    let queryFilter: QueryFilterParams = {
      order_by: event ? this.activeSort(event) : ''
      // employee_status: 'inactive'
    };
    if (!this.sortColumnFlag) {
      this.sortFieldName = this.dataFilter?.order_by ? this.dataFilter.order_by.split(':')[1] : '';
      this.sortOrderNumber = this.dataFilter?.order_by ? (this.dataFilter.order_by.split(':')[0] === 'asc' ? 1 : -1) : 1;
    }

    if (this.dataFilter.rollingOption) {
      const { startDate, endDate } = this.getStartDateEndDateFromRolling(this.dataFilter.rollingOption);
      this.dataFilter.util_start_date = this.datePipe.transform(startDate.toString(), AppConstants.format);
      this.dataFilter.util_end_date = this.datePipe.transform(endDate.toString(), AppConstants.format);
      this.dataFilter.year = null;
      this.dataFilter.quarter = null;
      this.dataFilter.start_month = null;
      this.dataFilter.end_month = null;
    }

    if (this.dataFilter.year) {
      const startEndDate = this.getStartEndDateFromYear(this.dataFilter.year);
      this.dataFilter.util_start_date = this.datePipe.transform(startEndDate.start_date.toString(), AppConstants.format);
      this.dataFilter.util_end_date = this.datePipe.transform(startEndDate.end_date.toString(), AppConstants.format);
      this.dataFilter.quarter = null;
      this.dataFilter.rollingOption = null;
      this.dataFilter.start_month = null;
      this.dataFilter.end_month = null;
    }
    if (this.dataFilter.quarter) {
      const startEndDate = this.getStartEndDateFromQuarter(this.dataFilter.quarter);
      this.dataFilter.util_start_date = this.datePipe.transform(startEndDate.start_date, AppConstants.format);
      this.dataFilter.util_end_date = this.datePipe.transform(startEndDate.end_date, AppConstants.format);
      this.dataFilter.year = null;
      this.dataFilter.rollingOption = null;
      this.dataFilter.start_month = null;
      this.dataFilter.end_month = null;
    }

    if (this.dataFilter.tags) {
      this.treeViewSelectedTags = this.getExtractedTagsFromSelectedTags(this.dataFilter.tags.split(','));
    }

    if (this.dataFilter.tags) {
      queryFilter.tags = this.dataFilter.tags;
    }
    if (!this.selectedUtiliuzation) {
      this.selectedUtiliuzation = this.dataFilter.selectedUtilizationToView;
    } else {
      this.dataFilter.selectedUtilizationToView = this.selectedUtiliuzation;
    }

    if (this.dataFilter.filterView === 'table') {
      this.switchToCalendarView();
    } else if (this.dataFilter.filterView === 'calendar') {
      this.switchToTableView();
    }

    if (this.dataFilter) {
      this.cacheFilter.setCacheFilters({ ...this.dataFilter }, 'bench-report');
      for (const [key] of Object.entries(this.dataFilter)) {
        if (key === 'employee_status' && (this.dataFilter[key]?.length === 2 || this.dataFilter[key]?.length === 0)) {
          queryFilter[`${key}`] = 'all';
        } else {
          queryFilter[`${key}`] = this.dataFilter[key];
        }
        if (!queryFilter.tags && !this.selectedTags.length) {
          this.treeViewSelectedTags = [];
        }

        delete queryFilter['date'];
        delete queryFilter['name'];
        delete queryFilter['start_month'];
        delete queryFilter['end_month'];
        delete queryFilter['order_by'];
        delete queryFilter['value'];
        delete queryFilter['year'];
        // delete queryFilter['employeeName'];
      }
    }
    if (!this.dataFilter.util_start_date && !this.dataFilter.util_end_date) {
      const date = new Date();
      queryFilter.util_start_date = this.datePipe.transform(new Date(date.getFullYear(), date.getMonth(), 1), AppConstants.format);
      queryFilter.util_end_date = this.datePipe.transform(new Date(date.getFullYear(), date.getMonth() + 3, 0), AppConstants.format);
    }
    queryFilter = this.queryStringUtil(queryFilter);
    this.preparingCalendarData = true;
    this.subscriptionManager.add(
      this.utilizationService.getBenchReport(queryFilter).subscribe(
        async (res) => {
          this.benchReportData = res.body.data?.employees ? res.body.data?.employees : [];
          this.DisplayUtilization();
          if (this.dataFilter.selectedAllocationType.includes('Over Allocated') || this.dataFilter.selectedAllocationType.includes('Under Allocated')) {
            this.calculatetAllocation();
          }
          this.makeRowsSameHeight();
          this.prepareHeaders(queryFilter);
          await this.prepareCalendarWithProjectsAndAllocations();
          this.preparingCalendarData = false;
          this.loading = false;
          this.loadingReport = false;
          this.cdf.detectChanges();
        },
        () => this.loading$.next(false)
      )
    );
  }

  makeRowsSameHeight() {
    setTimeout(() => {
      if (document.getElementsByClassName('p-datatable-scrollable-wrapper').length) {
        const wrapper = document.getElementsByClassName('p-datatable-scrollable-wrapper');
        for (var i = 0; i < wrapper.length; i++) {
          const w = wrapper.item(i) as HTMLElement;
          const frozen_rows: any = w.querySelectorAll('.p-datatable-frozen-view tr');
          const unfrozen_rows: any = w.querySelectorAll('.p-datatable-unfrozen-view tr');
          for (let i = 0; i < frozen_rows.length; i++) {
            if (frozen_rows[i].clientHeight > unfrozen_rows[i].clientHeight) {
              unfrozen_rows[i].style.height = frozen_rows[i].clientHeight + 'px';
            } else if (frozen_rows[i].clientHeight < unfrozen_rows[i].clientHeight) {
              frozen_rows[i].style.height = unfrozen_rows[i].clientHeight + 'px';
            }
          }
        }
        this.layoutConfigService.updateHeight$.next(true);
        this.height = height;
      }
    });
  }

  startMonthSelected(event) {
    this.dateError = false;
    this.dateRequired = false;
    this.dataFilter.util_start_date = this.datePipe.transform(event, AppConstants.format);
    this.dataFilter.start_month = moment(this.dataFilter.util_start_date).toDate();
    if (this.dataFilter.util_start_date && this.dataFilter.util_end_date) {
      if (new Date(this.dataFilter.util_start_date) > new Date(this.dataFilter.util_end_date)) {
        this.dateError = true;
      }
    }
  }

  endMonthSelected(event) {
    this.dateError = false;
    this.dateRequired = false;
    let date = new Date(event);
    date = new Date(date.getFullYear(), date.getMonth() + 1, 0);
    this.dataFilter.util_end_date = this.datePipe.transform(date, AppConstants.format);
    this.dataFilter.end_month = moment(this.dataFilter.util_end_date).toDate();
    if (this.dataFilter.util_start_date && this.dataFilter.util_end_date) {
      if (new Date(this.dataFilter.util_start_date) > new Date(this.dataFilter.util_end_date)) {
        this.dateError = true;
      }
    }
  }

  getAllDates(startDate, endDate) {
    const dArray = [];
    let cDate = new Date(startDate);
    while (cDate <= endDate) {
      // Adding the date to array
      const tableHeader: TableHeader = {
        field: `${MONTH_NAMES[cDate.getMonth()]} ${cDate.getFullYear()}`,
        sort: true,
        month: cDate.getMonth() + 1,
        monthLabel: `${MONTH_NAMES[cDate.getMonth()]} ${cDate.getFullYear()}`,
        year: cDate.getFullYear().toString(),
        id: Number(`${cDate.getFullYear()}${cDate.getMonth()}`)
      };
      dArray.push(tableHeader);
      // Increment the date by 1 month
      cDate = new Date(cDate.getFullYear(), cDate.getMonth() + 1, 1);
    }
    return dArray;
  }

  switchToTableView() {
    this.updatingButton = true;
    for (const button of this.buttons) {
      if (button.isSwitcherButton) {
        if (button.viewType === 'calendar') {
          button.isActive = true;
          button.btnSvg = 'calendar-active';
          this.tableButtonActive = false;
        } else {
          button.isActive = false;
          button.btnSvg = 'table';
        }
      }
    }
    this.calendarButtonActive = true;
    this.tableButtonActive = false;
    this.updatingButton = false;
    this.cdf.detectChanges();
  }

  bindPositionToSelectedPosition(resource: EmployeeData, month) {
    this.selectedPosition = [];
    const params = {
      start_date: month.start,
      end_date: month.end
    };
    this.subscriptionManager.add(
      this.utilizationService.getActiveEmployees(resource.employee.id, params).subscribe((res) => {
        if (res.body.data.positions.length > 0) {
          const pos = res.body.data.positions;
          this.selectedPosition = pos.map((p) => ({
            project: p.position.project,
            start_date: p.position.start_date,
            end_date: p.position.end_date
          }));
          this.cdf.detectChanges();
        }
      })
    );
  }

  switchToCalendarView() {
    this.updatingButton = true;
    for (const button of this.buttons) {
      if (button.isSwitcherButton) {
        if (button.viewType === 'table') {
          button.isActive = true;
          button.btnSvg = 'table-active';
        } else {
          button.isActive = false;
          button.btnSvg = 'calendar';
        }
      }
    }
    this.calendarButtonActive = false;
    this.tableButtonActive = true;
    this.updatingButton = false;
    this.cdf.detectChanges();
  }

  prepareCalendarWithProjectsAndAllocations(): Promise<void> {
    const redColor = '#EA3B52';
    const greenColor = '#4b99a2';
    return new Promise((resolve) => {
      let tempCalPosition: ResorceType;
      let tempCalPositionEvent: any;
      const tempResource = [];
      const tempEvents = [];
      for (const employee of this.benchReportData) {
        tempCalPosition = {
          id: employee.employee.id,
          name: `${employee.employee.first_name}`,
          customer: `${employee.employee?.last_name}`,
          color: employee.employee.employee_type.name ? greenColor : redColor
        };

        for (let i = 0; i < employee.employee.utilizations.length; i++) {
          tempCalPositionEvent = {
            start: moment([employee.employee.utilizations[i]?.utilization.year, employee.employee.utilizations[i].utilization.month - 1]).format('YYYY-MM-DD'),
            end: moment([employee.employee.utilizations[i].utilization.year, employee.employee.utilizations[i].utilization.month - 1])
              .clone()
              .endOf('month')
              .format('YYYY-MM-DD'),
            resource: employee.employee.id,
            color: Number(employee.employee.utilizations[i].utilization.utilization) * 100 <= 50 ? redColor : greenColor,
            title: employee.employee.utilizations[i].utilization.display_utilization
          };
          tempEvents.push(tempCalPositionEvent);
        }

        for (let i = 0; i < tempEvents.length; i++) {
          if (tempEvents[i].resource === employee.employee.id && tempEvents[i]?.title === tempEvents[i + 1]?.title) {
            tempEvents[i].end = tempEvents[i + 1].end;
            tempEvents.splice(i + 1, 1);
            i = i - 1;
          }
        }

        tempResource.push(tempCalPosition);
        this.myEvents = tempEvents;
      }
      this.myResources = tempResource;

      this.cdf.detectChanges();
      resolve();
    });
  }

  onAction(button: ButtonParams) {
    if (button.action) {
      button.action();
      this.dataFilter.filterView = button.viewType;
      this.defaultView = button.viewType;
      this.cacheFilter.setCacheFilters(this.dataFilter, 'bench-report');
    } else {
      this.router.navigate([button.redirectPath]);
    }
  }

  prepareHeaders(queryFilter) {
    this.tableHeaders = [];
    this.exportReportData = [];
    this.excelExportReportData = [];
    this.setExportHeaders();
    const sdate = new Date(queryFilter.util_start_date);
    sdate.setDate(sdate.getDate() + 1);
    if (this.benchReportData.length) {
      this.tableHeaders = this.getAllDates(sdate, new Date(queryFilter.util_end_date));
      for (const employee of this.benchReportData) {
        let exportData = {};
        let excelExportData = {};
        exportData = {
          name: employee?.employee?.first_name + ' ' + employee?.employee?.last_name,
          employee_type: employee?.employee?.employee_type?.name,
          skill_set: this.getSkillSet(employee.employee.position_types),
          tags: this.getExtractedTagsFromSelectedTags(employee.employee.tags).toString()
        };
        excelExportData = { ...exportData };
        for (const utilizations of employee?.employee?.utilizations) {
          const month = utilizations.utilization.month;
          const year = utilizations.utilization.year;
          const tableHeader: TableHeader = {
            month: month,
            monthLabel: `${MONTH_NAMES[month - 1]} ${year}`,
            year: year,
            id: Number(`${year}${month - 1}`)
          };
          exportData[tableHeader.monthLabel] = utilizations.utilization.display_utilization;
          excelExportData[tableHeader.monthLabel] = utilizations.utilization.display_utilization;
          utilizations.utilization[tableHeader.monthLabel] = utilizations.utilization.utilization;
        }
        this.exportReportData.push(exportData);
        this.excelExportReportData.push(excelExportData);
      }
      this.tableHeaders.forEach((header) => {
        this.exportPdfColumns.push({
          title: header.monthLabel,
          dataKey: header.monthLabel
        });
        this.excelHeaders[0][header.monthLabel] = header.monthLabel;
        this.csvCols.push(header.monthLabel);
      });
    }
    this.layoutConfigService.updateHeight$.next(true);
    this.height = height;
    this.addZeroToNonAvailableData();
    this.cdf.detectChanges();
  }

  addZeroToNonAvailableData() {
    const reportData = [];
    const excelExportData = [];
    for (const data of this.exportReportData) {
      const keys = Object.keys(data);
      this.tableHeaders.forEach((header) => {
        if (!keys.includes(header.monthLabel)) {
          data[header.monthLabel] = '--';
        }
      });
      reportData.push(data);
    }
    for (const data of this.excelExportReportData) {
      const keys = Object.keys(data);
      this.tableHeaders.forEach((header) => {
        if (!keys.includes(header.monthLabel)) {
          data[header.monthLabel] = 0;
        }
      });
      excelExportData.push(data);
    }
    this.exportReportData = reportData;
    this.excelExportReportData = excelExportData;
  }

  getValues(header, data) {
    if (this.sorting) {
      this.sortFieldName = this.tableHeaders[0].monthLabel;
      this.sorting = false;
    }
    for (const utilizations of data?.employee?.utilizations) {
      const keys = Object.keys(utilizations.utilization);
      if (keys.includes(header.monthLabel)) {
        return utilizations.utilization.display_utilization;
      }
    }
    return '--';
  }

  DisplayUtilization() {
    for (const employee of this.benchReportData) {
      for (const utilization of employee.employee.utilizations) {
        switch (this.selectedUtiliuzation) {
          case 'Dollars':
            utilization.utilization.display_utilization = new Intl.NumberFormat('en-US', {
              style: 'currency',
              currency: 'USD',
              minimumFractionDigits: 0,
              maximumFractionDigits: 0
            }).format(+utilization.utilization.amount);
            break;
          case 'Percent':
            utilization.utilization.display_utilization = `${Math.round(Number(utilization.utilization.utilization) * 100)}%`;
            break;
          case 'Hours':
            utilization.utilization.display_utilization = `${Math.round(Number(utilization.utilization.employee_hours) - Number(utilization.utilization.position_hours))}`;
            break;
        }
      }
    }
  }

  calculatetAllocation() {
    const filteredEmployees = this.benchReportData.filter((employee) => {
      const { utilizations } = employee.employee;

      const isOverAllocated = this.dataFilter.selectedAllocationType.includes('Over Allocated');
      const isUnderAllocated = this.dataFilter.selectedAllocationType.includes('Under Allocated');

      const overAllocationValue = this.dataFilter.selectedoverAllocationValue || 100;
      const underAllocationValue = this.dataFilter.selectedunderAllocationValue || 80;

      const hasOverAllocatedUtilization = isOverAllocated && utilizations.some((utilization) => +utilization.utilization.utilization * 100 > overAllocationValue);

      const hasUnderAllocatedUtilization = isUnderAllocated && utilizations.some((utilization) => +utilization.utilization.utilization * 100 < underAllocationValue);

      if (isOverAllocated && isUnderAllocated) {
        return hasOverAllocatedUtilization || hasUnderAllocatedUtilization;
      }
      if (isOverAllocated && hasOverAllocatedUtilization) {
        return true;
      }
      if (isUnderAllocated && hasUnderAllocatedUtilization) {
        return true;
      }
      return false;
    });

    this.benchReportData = filteredEmployees;
  }

  setDefaultView(view: string) {
    this.defaultView = view;
    this.dataFilter.filterView = view;
  }

  validateSecondInput(a: any) {
    if (+this.dataFilter.selectedunderAllocationValue >= +this.dataFilter.selectedoverAllocationValue) {
      this.dataFilter.selectedunderAllocationValue = this.dataFilter.selectedoverAllocationValue - 1 >= 1 ? this.dataFilter.selectedoverAllocationValue - 1 : 0;
      this.cdf.detectChanges();
    }
  }

  activeSort(event: SortEvent) {
    if (event.field) {
      if (event.order === 1) {
        return 'asc:' + event.field;
      } else {
        return 'desc:' + event.field;
      }
    }
    return null;
  }

  // used to take query string param and removes values which are not set in the query string
  queryStringUtil(queryStringParam: QueryFilterParams, isSaveFilterQuery: boolean = false) {
    const queryFilter: QueryFilterParams = {};
    if (queryStringParam) {
      for (const [key] of Object.entries(queryStringParam)) {
        if (queryStringParam[key] === '' || queryStringParam[key] === null || queryStringParam[key] === undefined) {
          delete queryStringParam[key];
        }
        if (key === 'rollingOption' && queryStringParam['util_start_date'] && queryStringParam['util_end_date'] && isSaveFilterQuery) {
          delete queryStringParam['util_start_date'];
          delete queryStringParam['util_end_date'];
        }
      }
    }
    return queryStringParam;
  }

  openSideBar() {
    this.sidebarParams = { template: this.el };
    // this.sidebarParams.template.open();
    this.openFilter = true;
  }

  onClose() {
    // this.sidebarParams.template.close();
    this.openFilter = false;
    this.cdf.detectChanges();
    this.dateRequired = false;
    this.dateError = false;
  }

  defaultFilters() {
    this.showEmployeeFilter = false;
    if (!this.dataFilter?.project_statuses) {
      this.tags = [];
      this.dataFilter.project_statuses = this.defaultStatuses.toString();
      this.selectedUtiliuzation = 'Percent';
      this.defaultView = 'table';
      this.dataFilter.filterView = 'table';
      this.dataFilter.employee_status = 'activefuture';
      this.projectStatus = this.defaultStatuses;
      this.tags.push({
        label: 'Project Status',
        value: this.dataFilter.project_statuses,
        key: ['project_statuses']
      });
    }
    if (!this.dataFilter?.util_start_date && !this.dataFilter.util_end_date) {
      const date = new Date();
      this.dataFilter.rollingOption = 'Current plus 2 months';
      if (this.dataFilter.rollingOption) {
        this.tags.push({
          label: 'Rolling',
          value: this.dataFilter.rollingOption.toString(),
          key: ['start_date_lte', 'end_date_gte', 'rollingOption']
        });
      }
      this.applyAllFilter();
    }
  }

  clearFilters() {
    this.dataFilter = new IFilter();
    this.projectStatus = null;
    this.employeeName = null;
    this.selectedEmployeeName = [];
    this.selectedTags = [];
    this.showEmployeeFilter = false;
    this.dateRequired = false;
    this.dateError = false;
    this.dataFilter.project_statuses = this.defaultStatuses.toString();
    this.selectedUtiliuzation = 'Percent';
    this.defaultView = 'table';
    this.dataFilter.filterView = 'table';
    this.dataFilter.selectedAllocationType = [];
    this.dataFilter.selectedoverAllocationValue = 100;
    this.dataFilter.selectedunderAllocationValue = 80;
    this.projectStatus = this.defaultStatuses;
    this.dataFilter.employee_status = 'activefuture';
    const date = new Date();
    this.dataFilter.rollingOption = 'Current plus 2 months';
  }

  filterReport() {
    this.loading = true;

    if (this.dataFilter.start_month || this.dataFilter.end_month) {
      if (!this.dataFilter.start_month) {
        this.dateRequired = true;
      }
      if (!this.dataFilter.end_month) {
        this.dateRequired = true;
      }
    }
    if (this.dataFilter?.value?.value) {
      this.dataFilter.employee_ids = this.dataFilter.value.value;
    }
    if (!this.dateError && !this.dateRequired) {
      this.applyTags();
      this.loadReport();
      this.onClose();
    }
  }

  exportReport(type) {
    if (type === 'csv') {
      this.utilizationService.exportToCsv(this.exportReportData, 'benchReport', this.csvCols);
    }
    if (type === 'pdf') {
      this.utilizationService.exportPdf(this.exportPdfColumns, this.exportReportData, 'benchReport', 4);
    }
    if (type === 'excel') {
      this.utilizationService.exportExcel(this.excelHeaders, this.excelExportReportData, 'benchReport');
    }
  }

  showHelpData(data) {
    data.showHelpIconData = !data.showHelpIconData;
    this.loading$$.next(true);
    this.utilizationService.getEmployee(data.employee.id).subscribe((res) => {
      this.loading$$.next(false);
      data.employee.email = res?.data?.employee?.email;
      this.cdf.detectChanges();
    });
  }

  showPositionData(pop, data, col, columns) {
    // pop.open();
    this.utilizationEmployeeName = `${data?.employee?.first_name}${data?.employee?.last_name}`;
    this.activeEmployee = [];
    const dateFormat = 'yyyy-MM-dd';
    let params = {
      start_date: this.datePipe.transform(new Date(col.year, col.month - 1), dateFormat),
      end_date: this.datePipe.transform(new Date(col.year, col.month, 0), dateFormat)
    };
    this.subscriptionManager.add(
      this.utilizationService.getActiveEmployees(data.employee.id, params).subscribe((res) => {
        if (res.body.data.positions.length > 0) {
          let pos = res.body.data.positions;
          let emp = [];
          pos.map(async (p) => {
            let obj = {
              customer: await this.getCustomerInfo(p.position.project.id),
              project: p.position.project,
              start_date: p.position.start_date,
              end_date: p.position.end_date,
              allocation: p.position.daily_billable_hours
            };
            emp.push(obj);
          });
          this.activeEmployee = emp;
          this.loadingEmp = false;
        } else {
          this.loadingEmp = false;
        }
        this.utilizationDetails = true;
      })
    );
  }

  getCustomerInfo(projectId): Promise<Project> {
    return new Promise((resolve) => {
      if (projectId) {
        this.utilizationService.getProject(projectId).subscribe((res) => {
          resolve(res?.data);
        });
      } else {
        resolve(null);
      }
    });
  }

  getEmployeeIds() {
    if (this.dataFilter && this.dataFilter.name && this.dataFilter.name.value) {
      const paramsToRemove = ['offset', 'limit'];
      const param = this.removeParams(this.dataFilter.name.value.split('&'), paramsToRemove);
      this.subscriptionManager.add(
        this.utilizationService.getEmployeeIds(param).subscribe((res) => {
          if (res?.employee_ids) {
            this.dataFilter.employee_ids = res.employee_ids.join(',');
          } else {
            this.dataFilter.employee_ids = '';
          }
          this.cdf.detectChanges();
        })
      );
    }
  }

  removeParams(params, paramsToRemove) {
    return params
      .filter((param) => {
        const [key, value] = param.split('=');
        return !paramsToRemove.includes(key) && value !== '' && value !== null;
      })
      .join('&');
  }

  applyTags() {
    this.loading = true;
    this.tags = [];
    if (!this.dataFilter) {
      return;
    }

    if (this.dataFilter.util_start_date && this.dataFilter.util_end_date && !this.dataFilter.year && !this.dataFilter.quarter && !this.dataFilter.rollingOption) {
      const dateFormat = 'MM/dd/yyyy';
      const value = this.datePipe.transform(this.dataFilter.util_start_date, dateFormat) + ' - ' + this.datePipe.transform(this.dataFilter.util_end_date, dateFormat);
      this.tags.push({
        label: 'Date Range',
        value: value,
        key: ['util_start_date', 'util_end_date', 'start_month', 'end_month']
      });
    }

    if (this.dataFilter.rollingOption) {
      this.tags.push({
        label: 'Rolling',
        value: this.dataFilter.rollingOption.toString(),
        key: ['start_date_lte', 'end_date_gte', 'rollingOption']
      });
    }

    if (this.dataFilter.year) {
      this.tags.push({
        label: 'By Year',
        value: this.dataFilter.year.toString(),
        key: ['util_start_date', 'util_end_date', 'year']
      });
    }

    if (this.dataFilter.quarter) {
      this.tags.push({
        label: 'By Quater',
        value: this.dataFilter.quarter.toString(),
        key: ['util_start_date', 'util_end_date', 'quarter']
      });
    }

    if (this.dataFilter.name) {
      this.tags.push({
        label: 'Employee Group',
        value: this.dataFilter.name.name,
        key: ['name', 'employee_ids']
      });
    }

    if (this.dataFilter.value) {
      this.tags.push({
        label: 'Employee',
        value: this.dataFilter.value.name,
        key: ['value', 'employee_ids']
      });
    }

    if (this.dataFilter.employee_type_name) {
      this.tags.push({
        label: 'Consultant Type',
        value: this.dataFilter.employee_type_name,
        key: ['employee_type_name']
      });
    }
    if (this.dataFilter.project_statuses) {
      this.tags.push({
        label: 'Project Status',
        value: this.dataFilter.project_statuses
      });
    }
    if (this.dataFilter?.employee_status?.length) {
      this.tags.push({
        label: 'Employee Status',
        value: this.getEmployeeStatus(this.dataFilter.employee_status),
        key: ['employee_status']
      });
    }
    if (this.dataFilter?.employeeName?.length) {
      this.tags.push({
        label: 'Employee',
        value: this.selectedEmployeeName.join(','),
        key: ['employeeName', 'employee_ids']
      });
    }
  }

  // employeeStatus(type,event){
  //   if(event.target.checked){
  //     this.dataFilter.employee_status.push(type)
  //   }else{
  //     const index = this.dataFilter.employee_status.indexOf(type);
  //     this.dataFilter.employee_status.splice(index,1);
  //   }
  // }

  onRemoveStatusFilter(statusValue) {
    if (statusValue.length) {
      this.dataFilter.project_statuses = statusValue.join(',');
      this.projectStatus = statusValue;
    } else {
      this.dataFilter.project_statuses = null;
      this.projectStatus = null;
    }
    this.doFilterData();
  }

  onCloseFromFilterPanel(tagValue) {
    tagValue?.key?.forEach((key) => {
      if (key === 'employee_status') {
        this.dataFilter[key] = [];
      } else if (key === 'employeeName') {
        this.dataFilter[key] = null;
        this.employeeName = null;
        this.selectedEmployeeName = [];
      } else {
        this.dataFilter[key] = null;
      }
    });
    this.doFilterData();
  }

  doFilterData() {
    this.loading = true;
    this.applyTags();
    this.loadReport();
  }

  getProjectStatus() {
    this.subscriptionManager.add(
      this.projectService.getProjectStatus().subscribe((res) => {
        const projectStatuses = res.data?.project_statuses || [];
        this.defaultStatuses = projectStatuses.filter((status) => status.project_status.is_default).map((status) => status.project_status.name);

        this.statuses = projectStatuses.map((status) => ({
          label: status.project_status.name,
          value: status.project_status.name
        }));
        this.defaultFilters();
      })
    );
  }

  ngAfterViewInit() {
    if (this.multiSelectComp) {
      this.multiSelectComp.options = this.statuses;
    }
    if (this.multiSelectComp2) {
      this.multiSelectComp2.options = this.employeeList;
    }
    if (this.el) {
      this.loading$.next(false);
      this.loading = false;
      this.sidebarParams = { template: this.el };
      // this.sidebarParams.template.toggle();
      this.openFilter = true;
      // this.openFilter = !this.openFilter;
    }
  }

  statusSelected(event) {
    this.dataFilter.project_statuses = event?.value?.toString();
  }

  employeeSelected(event) {
    this.dataFilter.employee_ids = event?.value?.toString();
    let employeeName = this.employeeList.filter((elist) => elist.value == event.itemValue);
    if (employeeName.length && employeeName[0]?.label) {
      if (this.selectedEmployeeName.includes(employeeName[0]?.label)) {
        this.selectedEmployeeName = this.selectedEmployeeName.filter((emp) => emp !== employeeName[0].label);
      } else {
        this.selectedEmployeeName.push(employeeName[0]?.label);
      }
    }
    this.dataFilter.employeeName = this.selectedEmployeeName;
  }

  getFixCssClass(col) {
    return 'fix-col-3';
  }
  getDynCssClass(col) {
    if (col.length <= 3) {
      return 'dynamic-col-3';
    } else {
      return 'dynamic-col-more';
    }
  }

  resetFilter() {
    this.dataFilter = new IFilter();
    this.cacheFilter.resetCacheFilters('bench-report');
    this.projectStatus = this.defaultStatuses;
    this.selectedEmployeeName = [];
    this.employeeName = null;
    this.tags = [];
    this.showEmployeeFilter = false;
    this.dataFilter.employee_status = 'activefuture';
    this.selectedUtiliuzation = 'Percent';
    this.defaultView = 'table';
    this.dataFilter.filterView = 'table';
    this.dataFilter.selectedAllocationType = [];
    this.dataFilter.selectedoverAllocationValue = 100;
    this.dataFilter.selectedunderAllocationValue = 80;
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: { filterId: null },
      queryParamsHandling: 'merge'
    });
    // reset the filter selection as well
    this.selectedFilterFormControl = new FormControl('');
    this.finalTagsAlongWithTheCategory = [];
    this.selectedTags = [];
    this.dataFilter.rollingOption = 'Current plus 2 months';
    this.defaultFilters();
    this.loadReport();
  }

  openSaveFilterList() {
    this.showFilterListDialog = true;
    this.showSavedFilter = true;
    this.cdf.detectChanges();
  }
  applyAllFilter() {
    if (this.dataFilter.util_start_date && this.dataFilter.start_month) {
      this.dataFilter.start_month = moment(this.dataFilter.util_start_date).toDate();
    }
    if (this.dataFilter.util_end_date && this.dataFilter.end_month) {
      this.dataFilter.end_month = moment(this.dataFilter.util_end_date).toDate();
    }
    if (this.dataFilter?.emp_grp_name && this.dataFilter?.emp_grp_value) {
      this.dataFilter.name = {
        name: this.dataFilter.emp_grp_name,
        value: this.dataFilter.emp_grp_value.replace(/%3D/g, '=').replace(/%26/g, '&')
      };
      delete this.dataFilter.emp_grp_name;
      delete this.dataFilter.emp_grp_value;
      this.showEmployeeFilter = true;
    } else if (this.dataFilter.employeeName && this.dataFilter.employee_ids) {
      this.employeeName = this.dataFilter.employee_ids.split(',');
      this.selectedEmployeeName = this.dataFilter.employeeName.toString().replace(/%2C/g, ',').split(',');
    }
    if (this.dataFilter?.project_statuses) {
      this.dataFilter.project_statuses = this.dataFilter.project_statuses.replace(/%2C/g, ',');
      this.projectStatus = this.dataFilter.project_statuses.split(',');
    }

    if (this.dataFilter.employee_ids) {
      this.dataFilter.employee_ids = this.dataFilter.employee_ids.replace(/%2C/g, ',');
    }

    if (this.dataFilter.selectedAllocationType) {
      const savedAlloction: string | any = this.dataFilter.selectedAllocationType;
      if (typeof this.dataFilter.selectedAllocationType === 'string') this.dataFilter.selectedAllocationType = savedAlloction.replace(/%2C/g, ',').split(',');
    } else {
      this.dataFilter.selectedAllocationType = [];
    }

    if (this.dataFilter.tags) {
      this.dataFilter.tags = this.dataFilter.tags.replace(/%3A/g, ':').replace(/%2C/g, ',');
    }

    if (this.dataFilter.name) {
      this.showEmployeeFilter = true;
    }

    this.applyTags();
    this.cdf.detectChanges();
  }
  applyFilter() {
    this.showSavedFilter = false;
    this.dataFilter = JSON.parse('{"' + decodeURI(this.selectedFilter.query_filter.query_string).replace(/"/g, '\\"').replace(/&/g, '","').replace(/=/g, '":"') + '"}');
    this.applyAllFilter();
    this.selectedFilter = null;
    this.filteredFilters.data.query_filters = this.availableFilters;
    this.loadReport();
    this.closeModal();
  }

  closeModal() {
    this.showFilterListDialog = false;
    this.selectedFilter = null;
    this.showEditDialog = false;
    this.editFilterObj = null;
    this.showNameError = false;
    this.showDeleteDialog = false;
    this.deleteFilterObj = null;
    this.showShareDialog = false;
    this.shareFilterObj = null;
    this.utilizationDetails = false;
  }

  onSaveFilter() {
    let filter = JSON.parse(JSON.stringify(this.dataFilter));
    filter = this.queryStringUtil(filter, true);
    if (this.dataFilter?.name?.name) {
      filter.emp_grp_name = this.dataFilter?.name?.name;
      filter.emp_grp_value = this.dataFilter?.name?.value;
    }
    if (this.dataFilter?.value?.name) {
      filter.employee_name = this.dataFilter?.value?.name;
      filter.employee_id = this.dataFilter?.value?.value;
    }
    const requestObject: SaveFilter = {
      query_string: this.serialize(filter),
      resource: 'benchReport'
    };
    const dialogTitle = 'Save Filter Group';
    const dialogRef = this.layoutUtilsService.saveClientGroupName(dialogTitle, requestObject);
    dialogRef.afterClosed().subscribe((filtersResponse) => {
      if (filtersResponse) {
        this.getStoredFilters();
        setTimeout(() => {
          // this.sidebarParams.template.close();
          this.openFilter = false;
          this.cdf.detectChanges();
        }, 900);
        this.layoutUtilsService.showActionNotification('Filter has been saved successfully', AlertType.Success);
      }
    });
  }
  // converts the object in to query param string
  serialize = (obj) => {
    const str = [];
    for (const p in obj) {
      if (obj.hasOwnProperty(p)) {
        str.push(encodeURIComponent(p) + '=' + encodeURIComponent(obj[p]));
      }
    }
    return str.join('&');
  };

  getStoredFilters() {
    const requestObject = {
      resource: 'benchReport'
    };
    this.subscriptionManager.add(
      this.utilizationService.getStoredFilters(requestObject).subscribe(
        (res: ISavedFilterList) => {
          this.loading = false;
          this.sharedFilters = [];
          this.myFilters = [];
          this.filteredFilters = JSON.parse(JSON.stringify(res)); // to deep copy the object
          this.sharedFilters = this.filteredFilters?.data?.query_filters?.filter((q) => q.query_filter.is_shared === true);
          this.myFilters = this.filteredFilters?.data?.query_filters?.filter((q) => q.query_filter.is_shared === false);
          this.availableFilters = res.data.query_filters;
          this.cdf.detectChanges();
          this.routerListener();
        },
        () => (this.loading = false)
      )
    );
  }
  closePopOver(p) {
    // p.toggle();
    this.utilizationDetails = false;
  }

  showSaveEmployeeFilterSelected() {
    this.showEmployeeFilter = !this.showEmployeeFilter;
    this.dataFilter.name = null;
    this.dataFilter.employee_ids = null;
    this.dataFilter.employeeName = [];
    this.employeeName = [];
    if (this.showEmployeeFilter) {
      this.dataFilter.employee_status = null;
    } else {
      this.dataFilter.employee_status = 'activefuture';
    }

    this.cdf.detectChanges();
  }

  getStartDateEndDateFromRolling(rollingOption: string) {
    const currentDate = new Date();
    const rollingRelativeMonth = rollingOption.split(' ')[1];
    const rollingMonths = parseInt(rollingOption.split(' ')[2]);

    const startDate = new Date(currentDate);
    startDate.setDate(1);

    const endDate = new Date(startDate);
    endDate.setMonth(startDate.getMonth() + rollingMonths + 1, 0);

    if (rollingRelativeMonth === 'minus') {
      startDate.setMonth(currentDate.getMonth() - 1);
      endDate.setMonth(currentDate.getMonth() + rollingMonths, 0);
      endDate.setFullYear(currentDate.getFullYear());
    }

    return { startDate, endDate };
  }

  getStartEndDateFromYear(year) {
    return {
      start_date: new Date(year, 0, 1, 0, 0, 0, 0),
      end_date: new Date(year, 11, 31, 11, 59, 59, 999)
    };
  }

  getStartEndDateFromQuarter(quarter) {
    const now = new Date();
    const quarterDates = {
      Q1: {
        start_date: new Date(now.getFullYear(), 0, 1, 0, 0, 0, 0),
        end_date: new Date(now.getFullYear(), 2, 31, 11, 59, 59, 999)
      },
      Q2: {
        start_date: new Date(now.getFullYear(), 3, 1, 0, 0, 0, 0),
        end_date: new Date(now.getFullYear(), 5, 30, 11, 59, 59, 999)
      },
      Q3: {
        start_date: new Date(now.getFullYear(), 6, 1, 0, 0, 0, 0),
        end_date: new Date(now.getFullYear(), 8, 30, 11, 59, 59, 999)
      },
      Q4: {
        start_date: new Date(now.getFullYear(), 9, 1, 0, 0, 0, 0),
        end_date: new Date(now.getFullYear(), 11, 31, 11, 59, 59, 999)
      }
    };
    return quarterDates[quarter];
  }

  searchFilters(filter: string) {
    this.filteredFilters.data.query_filters = filter.length
      ? this.availableFilters.filter((availableFilter) => availableFilter.query_filter.name.toLowerCase().includes(filter.toLowerCase()))
      : this.availableFilters;
    this.sharedFilters = this.filteredFilters?.data?.query_filters.filter((q) => q.query_filter.is_shared === true);
    this.myFilters = this.filteredFilters?.data?.query_filters.filter((q) => q.query_filter.is_shared === false);
  }

  editFilter(filter) {
    this.showEditDialog = true;
    this.editFilterObj = _.cloneDeep(filter);
  }
  shareFilter(filterOption) {
    this.showShareDialog = true;
    this.shareFilterObj = {
      ...filterOption,
      header: 'Share',
      text: `Do you want to share Filter "${filterOption.query_filter.name}" to all users publically?`
    };
    this.shareFilterObj.query_filter.is_shared = !this.shareFilterObj.query_filter.is_shared;
  }

  unShareFilter(filterOption) {
    this.showShareDialog = true;
    this.shareFilterObj = {
      ...filterOption,
      header: 'Unshare',
      text: `Do you want to unshare Filter "${filterOption.query_filter.name}" from all users?`
    };
    this.shareFilterObj.query_filter.is_shared = !this.shareFilterObj.query_filter.is_shared;
  }

  saveShareFilter() {
    this.isSubmitting = true;
    this.subscriptionManager.add(
      this.utilizationService.updateFilter(this.shareFilterObj.query_filter.id, this.shareFilterObj).subscribe(
        (res) => {
          this.layoutUtilsService.showActionNotification(this.shareFilterObj.query_filter.is_shared ? AppConstants.shareFilter : AppConstants.unShareFilter, AlertType.Success);
          this.isSubmitting = false;
          this.utilizationService.showNewSharedFilter.next('Utilization Report');
          this.closeModal();
          this.getStoredFilters();
          this.showSavedFilter = true;
          this.showFilterListDialog = true;
          this.cdf.detectChanges();
        },
        () => (this.isSubmitting = false)
      )
    );
  }
  inputFilterName() {
    this.showNameError = false;
  }

  deleteFilter(filterOption) {
    this.showDeleteDialog = true;
    this.deleteFilterObj = filterOption;
  }

  saveDeleteFilter() {
    this.isSubmitting = true;
    this.subscriptionManager.add(
      this.utilizationService.deleteStoredFilters(this.deleteFilterObj.query_filter.id).subscribe(
        (res) => {
          this.layoutUtilsService.showActionNotification(AppConstants.deleteFilter, AlertType.Success);
          this.isSubmitting = false;
          this.closeModal();
          this.getStoredFilters();
          this.showSavedFilter = true;
          this.showFilterListDialog = true;
          this.cdf.detectChanges();
          this.utilizationService.showNewSharedFilter.next('Utilization Report');
        },
        () => (this.isSubmitting = false)
      )
    );
  }

  saveEditFilter() {
    if (!this.editFilterObj.query_filter.name.length) {
      this.showNameError = true;
    } else {
      this.isSubmitting = true;
      this.subscriptionManager.add(
        this.utilizationService.updateFilter(this.editFilterObj.query_filter.id, this.editFilterObj).subscribe(
          (res) => {
            this.layoutUtilsService.showActionNotification(AppConstants.updateFilter, AlertType.Success);
            this.isSubmitting = false;
            this.closeModal();
            this.getStoredFilters();
            this.showSavedFilter = true;
            this.showFilterListDialog = true;
            this.cdf.detectChanges();
          },
          () => (this.isSubmitting = false)
        )
      );
    }
  }

  private routerListener() {
    this.activatedRoute.queryParamMap.subscribe((params: Params) => {
      // grab the filter id from the url if present we will make an api call to get the specific filter from that id.
      if (params.params.filterId) {
        this.queryFilterId = params.params.filterId;
        this.getTheFilterById();
      }
    });
  }

  // used to get the filter by its id
  getTheFilterById() {
    this.subscriptionManager.add(
      this.projectService.getTheFilterById(this.queryFilterId).subscribe(
        (res) => {
          // if we get some response only then we may try to apply filter
          if (res) {
            this.selectedFilter = res?.data;
            const filterValue = this.sharedFilters?.find((f) => JSON.stringify(f) === JSON.stringify(this.selectedFilter));
            if (filterValue) {
              this.selectedFilterFormControl.setValue(filterValue);
            }
            this.applyFilter();
            this.onFilterChangePreapareSelectedTreeNodes();
          }
        },
        (error) => {
          this.layoutUtilsService.showActionNotification(AppConstants.problemFetchingFilterById, AlertType.Error);
        }
      )
    );
  }

  copyLinkToTheFilter(filterId: number) {
    const filterHolder = document.createElement('textarea');
    filterHolder.style.position = 'fixed';
    filterHolder.style.left = '0';
    filterHolder.style.top = '0';
    filterHolder.style.opacity = '0';
    // construct the full url to be copied
    // e.g. http://localhost:4200/project/manage?filterId=3
    const hostName = `${window.location.protocol}${window.location.host}`;
    const filterString = `${hostName}${this.router.url.split('?')[0]}/?filterId=${filterId}`;

    filterHolder.value = filterString;
    document.body.appendChild(filterHolder);
    filterHolder.focus();
    filterHolder.select();
    document.execCommand('copy');
    document.body.removeChild(filterHolder);
    this.layoutUtilsService.showActionNotification(AppConstants.filterLinkCopied, AlertType.Success);
  }

  applySelectedFilterAndUpdateUrl() {
    this.showSavedFilter = false;
    this.showFilterListDialog = false;
    this.selectedFilter = this.selectedFilterFormControl.value;
    this.onClose();
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: { filterId: this.selectedFilter?.query_filter?.id },
      queryParamsHandling: 'merge'
    });
  }

  getEmployeeStatus(status) {
    switch (status) {
      case 'all':
        return 'All Employees';
      case 'active':
        return 'Current Employees';
      case 'activefuture':
        return 'Current & Future Employees';
      case 'inactive':
        return 'Inactive Employees';
    }
  }
}
