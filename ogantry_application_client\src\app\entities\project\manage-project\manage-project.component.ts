import { Component, OnInit, ViewChild, ChangeDetectionStrategy, ChangeDetectorRef, AfterViewInit, OnDestroy } from '@angular/core';
import { Table } from 'primeng/table';
import { MessageService, TableState, ConfirmationService } from 'primeng/api';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { ButtonParams } from '@shared/models';
import { ClientService } from '@entities/client/client.service';
import { HttpResponse } from '@angular/common/http';
import { ProjectService } from '@entities/project/project.service';
import { FormControl } from '@angular/forms';
import { LayoutUtilsService } from '@shared/services/layout-utils.service';
import { AlertType } from '@shared/models/alert-type.enum';
import { DatePipe } from '@angular/common';
import { IFilter, Project, ISavedFilterList, QueryFilter, QueryFilterParams, ProjectList, SaveFilter, BillingType, BillingTypes } from '@entities/project/project.model';
import { KtDialogService } from '@shared/services';
import { Debounce } from '@shared/decorators/debounce.decorator';
import { GetSetCacheFiltersService } from '@shared/services/get-set-cache-filters.service';
import { MultiSelect } from 'primeng/multiselect';
import { Calendar } from 'primeng/calendar';
import { AppConstants } from '@shared/constants/app.constant';
import * as _ from 'lodash';
import { initialPermissions } from '@shared/models/permission.enum';
import { AdministrationService } from '@entities/administration/administration.service';
import { User } from '@shared/models/user.model';
import { AuthService } from '@auth/auth.service';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { Input } from '@angular/core';
import { GlobalDetailSubCategory, GlobalDetailTaggingCategory, GlobalDetailTags, SubCategory, TagCategory } from '@entities/administration/administration.model';
import { TreeViewStructure, TreeNode, PNGTree } from '@entities/administration/append-tags/tree-view-model';
import { MatDialog } from '@angular/material/dialog';
import { AppendTagsComponent } from '@entities/administration/append-tags/append-tags.component';
import { ColumnToggleService } from '@shared/services/column-toggle.service';
import { UtilizationService } from '@entities/utilization-management/utilization.service';
import { ComponentsType, FiledType } from '../../../@shared/models/component-type-enum';
import { ExtendedFormComponent } from '@shared/components/extended-form/extended-form.component';
import { extendedField } from '@shared/models/extended-field.model';
// import { SafePipe } from '@shared/pipes/safe.pipe';

const height = 'calc((var(--fixed-content-height, 1vh) * 100) - 130px)';

@Component({
  selector: 'app-manage-project',
  templateUrl: './manage-project.component.html',
  styleUrls: ['./manage-project.component.scss'],
  providers: [MessageService, DatePipe, ConfirmationService],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ManageProjectComponent extends SflBaseComponent implements OnInit, AfterViewInit, OnDestroy {
  filedType = FiledType;
  billingTypes = BillingTypes;
  queryFilterGlobal: any;
  projectObj?: Project;
  projectSetupForm?: Project;
  projectId: number = 0;
  service = '';
  componentType = ComponentsType;
  updateExtendFiled = '';
  showUpdateExtendFiledDialog = false;
  extendFieldsObj: any;
  globalObject: Array<string> = [];
  cardTitle = 'Project Management';
  showdescriptionDialog = false;
  cardSubTitle = null;
  extendFields: any;
  buttons: ButtonParams[] = [
    {
      btnClass: 'btn-save btn-add-wrapper',
      btnText: 'Add New',
      title: 'Create New Project',
      redirectPath: this.appRoutes.CREATE_PROJECT,
      permissions: [this.permissionModules.MANAGE_PROJECT]
    }
  ];
  globalDetailId: any;
  showFilter = false;
  splitButtonDropDownOption = {
    action: this.showFilterOption.bind(this),
    options: [
      {
        label: 'Get Stored Filters',
        icon: 'get-stored-filter-split-button-icon',
        command: () => {
          this.showSavedFilterDropDown();
        }
      },
      {
        label: 'Save Filter',
        icon: 'save-filter-split-button-icon',
        command: () => {
          this.onSave();
        }
      },
      {
        label: 'Reset Filter',
        icon: 'reset-filter-split-button-icon',
        command: () => {
          this.resetFilters();
        }
      }
    ]
  };
  projects: Project[] = [];
  height = height;
  statuses = [];

  loading = true;
  totalRecords: number;

  @ViewChild('dt') table: Table;
  dataFilter: IFilter = new IFilter();

  availableFilters: ISavedFilterList;
  filteredFilters: ISavedFilterList;
  selectedFilter: QueryFilter;
  showSavedFilter = false;
  selectedFilterFormControl = new FormControl('');
  deleteProjectId: number;
  showDeleteDialog = false;
  showPaginator: boolean;
  filteredFlag = false;
  selectedStatus = [];
  pageChangeFlag = false;
  sortColumnFlag = false;
  sortFieldName: string = 'name';
  sortOrderNumber: number = 1;
  dateFilter = [];
  @ViewChild('multiSelectComp') multiSelectComp: MultiSelect;
  @ViewChild('billingTypeSelect') billingTypeSelect: MultiSelect;
  @ViewChild('startCal') startDatePicker: Calendar;
  showCal = false;
  @ViewChild('startCal1') endDatePicker: Calendar;
  showCal1 = false;
  sharedFilters: QueryFilter[] = [];
  myFilters: QueryFilter[] = [];
  showEditDialog = false;
  editFilterObj: QueryFilter;
  showNameError = false;
  showDeleteFilterDialog = false;
  deleteFilterObj: QueryFilter;
  showShareDialog = false;
  shareFilterObj = null;
  roleConstToBeUpdated;
  currentPermissions = [];
  queryFilterId: number;
  showTagDialog = false;
  selectedTagToView;
  billingType = [];
  selectedBillingType = [];
  positionEdit = false;
  frozenCols = [];
  descriptionGlobalName: string;
  descriptionGlobal: string;
  _pCols: string[] = [];
  isShowHideColumns: boolean = false;
  _selectedColumns: any;
  selectedColumnsArray: any = [];
  draggedColumnIndex: any;
  selectedTags = [];
  treeViewSelectedTags = [];
  finalTagsAlongWithTheCategory: string[] = [];
  groupedCategory: TreeViewStructure;
  globalDetailsTaggingCategory: GlobalDetailTaggingCategory;
  tagSubCategory: SubCategory[] = [];
  globalDetailsTagSubCategory: GlobalDetailSubCategory;
  globalDetailsTag: GlobalDetailTags;
  taggingTags = [];
  tagCategories: TagCategory[] = [];
  selectedCategoriesTag: PNGTree[] = [];
  checkedProject: Project[];
  listSelected = false;
  extendFiledFilter: any = {};
  @Input() get selectedColumns(): any[] {
    return this._selectedColumns;
  }
  startDateKey = 'start-date';
  endDateKey = 'end-date';
  descriptionRows = 1;
  showHyperlinkNavigationDialog = false;
  linkValue = '';
  fieldDetail: extendedField;

  @ViewChild('childForm') extendedFieldFormComponent: ExtendedFormComponent;

  set selectedColumns(val: any) {
    setTimeout(() => {
      this.columnToggle.setSelectedColumns(this._selectedColumns, 'project');
      const col = this._selectedColumns;
      if (col) {
        this._selectedColumns = col.filter((val) => val.includes(val));
      } else {
        this._selectedColumns = this.frozenCols.filter((col) => val.includes(col));
      }
      this._pCols = col?.map((f) => f.field);
    }, 500);
  }

  constructor(
    private readonly dialog: MatDialog,
    private readonly projectService: ProjectService,
    private readonly datePipe: DatePipe,
    readonly clientService: ClientService,
    private readonly cdf: ChangeDetectorRef,
    private readonly layoutUtilsService: LayoutUtilsService,
    private readonly ktDialogService: KtDialogService,
    readonly confirmationService: ConfirmationService,
    private readonly cacheFilter: GetSetCacheFiltersService,
    private readonly administrationService: AdministrationService,
    private readonly authService: AuthService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly router: Router,
    private readonly columnToggle: ColumnToggleService,
    private readonly utilizationService: UtilizationService,
    private readonly adminService: AdministrationService
  ) {
    super();
  }

  ngOnInit() {
    this.getGlobalDetailsCategory();
    this.checkEmployeeFilterStatus('project');
    this.getStoredFilters();
    this.getProjectStatus();
    this.getProjectBillingType();
    this.dataFilter = {
      ...this.dataFilter,
      selectedFilterStartDate: 'gte',
      selectedFilterEndDate: 'gte'
    };
    if (this.cacheFilter.getCacheFilters(this.appConstants.MANAGE_SCREENS.PROJECT)) {
      this.dataFilter = this.cacheFilter.getCacheFilters(this.appConstants.MANAGE_SCREENS.PROJECT);
      if (this.dataFilter.start_date) {
        this.dataFilter.start_date = new Date(this.dataFilter.start_date);
      }

      if (this.dataFilter.end_date) {
        this.dataFilter.end_date = new Date(this.dataFilter.end_date);
      }
      if (!this.dataFilter.selectedFilterEndDate) {
        this.dataFilter.selectedFilterEndDate = 'gte';
      }
      if (!this.dataFilter.selectedFilterStartDate) {
        this.dataFilter.selectedFilterStartDate = 'gte';
      }
    }
    this.setDateFilter();
    this.managePermissions();
    this.getCategoryMasterData();
    this.frozenCols = [
      { field: 'name', monthLabel: 'Name', sort: true },
      { field: 'project_name', monthLabel: 'Project Name', sort: true },
      { field: 'start_date', monthLabel: 'Start Date', sort: true },
      { field: 'end_date', monthLabel: 'End Date', sort: true },
      { field: 'duration', monthLabel: 'Duration' },
      { field: 'bill_type', monthLabel: 'Bill Type' },
      { field: 'tags', monthLabel: 'Tags' },
      { field: 'status', monthLabel: 'Status', sort: true }
    ];

    this._selectedColumns = JSON.parse(localStorage.getItem('selectedColumnsArray'))?.project
      ? JSON.parse(localStorage.getItem('selectedColumnsArray'))['project']
      : this.frozenCols;
    this._pCols = this._selectedColumns.map((f) => f.field);
    this.projectService.showNewTags.subscribe((res) => {
      this.showCal = false;
      this.showCal1 = false;
      this.filteredFlag = true;
      this.loading = true;
      this.projects = [];
      this.loadProject(this.table);
      if (res === true) {
        this.checkedProject = [];
      }
    });

    this.frozenCols = [...this.frozenCols, { field: 'description', monthLabel: 'Description' }];
  }

  addTagsToMultipleProject() {
    const employee: any = [];
    for (const project of this.checkedProject) {
      employee.push(project.project.id);
    }
    const dialogRef = this.dialog.open(AppendTagsComponent, {
      data: {
        tags: [],
        employeeId: employee,
        title: 'Add Tag',
        flag: 'ADD TAGS TO CHECKED PROJECT'
      },
      width: '880px'
    });

    dialogRef.afterClosed().subscribe((res) => {
      if (res) {
        this.listSelected = false;
        this.checkedProject = [];
        this.cdf.detectChanges();
      }
    });
  }

  private routerListener() {
    this.activatedRoute.queryParamMap.subscribe((params: Params) => {
      // grab the filter id from the url if present we will make an api call to get the specific filter from that id.
      if (params.params.filterId) {
        this.queryFilterId = params.params.filterId;
        this.getTheFilterById();
      }
    });
  }

  openTagModal(tag) {
    this.showTagDialog = true;
    this.selectedTagToView = tag;
  }

  toggleWithCategory(tooltip, tag) {
    if (tooltip.isOpen()) {
      tooltip.close();
    } else {
      tooltip.open({ tag });
    }
  }

  getTagsCount(tagWithCategory: string, allowedTagLength?: boolean): string {
    if (allowedTagLength) {
      const tagArray = tagWithCategory.split('__');
      return tagArray[tagArray.length - 1];
    }
  }

  getTagCount(tags: string[]): string {
    return '+ ' + (tags?.length - 2);
  }

  getTagCategorySubCategory(tags: string): string {
    const tagArray = tags.split('__');
    const categoryName = tagArray[0];
    const subCategory = tagArray?.length > 2 ? tagArray[1] : null;
    if (subCategory) return `Category <strong>${categoryName}</strong> <br> Sub Category<strong>${subCategory}</strong>`;
    else return `Category <strong>${categoryName}</strong>`;
  }

  showHideColumns(type) {
    if (type == 'showColumns') {
      this.isShowHideColumns = true;
    } else {
      this.isShowHideColumns = false;
    }
  }

  onSelectColumsChange(event) {
    if (event) {
      this.columnToggle.setSelectedColumns(event.value, 'project');
      this._selectedColumns = event.value;
      this._pCols = event.value.map((f) => f.field);
    }
    this.isShowHideColumns = !this.isShowHideColumns;
  }

  getCategoryMasterData() {
    this.tagCategories = [];
    this.loading = true;
    this.subscriptionManager.add(
      this.administrationService.getTagCategories('TagCategoryManagement').subscribe(
        (res) => {
          this.loading = false;
          if (res?.data?.global_details) {
            const globalDetail = res?.data?.global_details;
            if (globalDetail && globalDetail[0]?.global_detail?.name === 'TagCategoryManagement') {
              this.globalDetailsTaggingCategory = globalDetail[0];
              this.tagCategories = globalDetail[0].global_detail.extended_fields.tagCategory;
              this.administrationService.setTagCategories(globalDetail[0].global_detail);
              this.getTagSubCategories();
            }
          }
        },
        () => (this.loading = false)
      )
    );
  }

  getGlobalDetailTags() {
    this.taggingTags = [];
    this.loading = true;
    this.subscriptionManager.add(
      this.administrationService.getTags('TagManagement').subscribe(
        (res) => {
          this.loading = false;
          if (res?.data?.global_details) {
            const globalDetail = res?.data?.global_details;
            if (globalDetail && globalDetail[0]?.global_detail?.name === 'TagManagement') {
              this.globalDetailsTag = globalDetail[0];
              this.taggingTags = globalDetail[0].global_detail.extended_fields.tags;
              this.administrationService.setTags(globalDetail[0].global_detail);
              this.combineCategoryAndSubCategory();
            }
          }
        },
        () => (this.loading = false)
      )
    );
  }

  initGroupingCategoryTags() {
    this.groupedCategory = { data: [] };
    for (const [index, category] of this.globalDetailsTaggingCategory.global_detail.extended_fields.tagCategory.entries()) {
      const dataCollection: TreeNode = new TreeNode();
      dataCollection.label = category.name;
      dataCollection.selectable = false;
      dataCollection.collapsedIcon = 'pi-chevron-right';
      dataCollection.expandedIcon = 'pi-chevron-down';
      dataCollection.expanded = true;
      if (category?.subTagCategory?.length) {
        for (const [subIndex, subCate] of category?.subTagCategory?.entries()) {
          dataCollection.children.push({
            label: subCate.name,
            collapsedIcon: 'pi-chevron-right',
            expandedIcon: 'pi-chevron-down',
            children: [],
            selectable: false,
            expanded: true
          });
          if (subCate?.tags?.length) {
            for (const [tagIndex, tag] of subCate?.tags?.entries()) {
              dataCollection.children[subIndex]?.children?.push({
                label: tag.name,
                collapsedIcon: 'pi-chevron-right',
                expandedIcon: 'pi-chevron-down',
                expanded: true
              });
            }
          }
        }
      }
      if (category?.tags?.length) {
        for (const [tagIndex, tag] of category?.tags?.entries()) {
          dataCollection.children?.push({
            label: tag.name,
            collapsedIcon: 'pi-chevron-right',
            expandedIcon: 'pi-chevron-down',
            expanded: true
          });
        }
      }
      this.groupedCategory.data.push(dataCollection);
    }
    this.onFilterChangePreapareSelectedTreeNodes();
  }

  getTagSubCategories() {
    this.tagSubCategory = [];
    this.loading = true;
    this.subscriptionManager.add(
      this.administrationService.getTagSubCategories('SubCategoryManagement').subscribe(
        (res) => {
          this.loading = false;
          if (res?.data?.global_details) {
            const globalDetail = res?.data?.global_details;
            if (globalDetail && globalDetail[0]?.global_detail?.name === 'SubCategoryManagement') {
              this.globalDetailsTagSubCategory = globalDetail[0];
              this.tagSubCategory = globalDetail[0].global_detail.extended_fields.subCategory;
              this.administrationService.setTagSubCategories(globalDetail[0].global_detail);
              this.getGlobalDetailTags();
            }
          }
        },
        () => (this.loading = false)
      )
    );
  }

  combineCategoryAndSubCategory() {
    for (const category of this.globalDetailsTaggingCategory.global_detail.extended_fields.tagCategory) {
      category.subTagCategory = [...this.tagSubCategory?.filter((subCate) => subCate?.parentCategoryId === category?.id)];
    }
    this.injectTagsToRespectiveCategoryOrSubCategory();
    this.initGroupingCategoryTags();
  }

  injectTagsToRespectiveCategoryOrSubCategory() {
    for (const category of this.globalDetailsTaggingCategory.global_detail.extended_fields.tagCategory) {
      for (const tag of this.taggingTags) {
        if (tag.tagCategory === category.id) {
          const subCateIndex = category.subTagCategory.findIndex((subCate) => subCate.id === tag.subTagCategory);
          if (subCateIndex !== -1) {
            category.subTagCategory[subCateIndex]['tags'].push(tag);
          } else {
            category.tags.push(tag);
          }
        }
      }
      category.subTagCategory = [...this.tagSubCategory?.filter((subCate) => subCate?.parentCategoryId === category?.id)];
    }
  }

  tagSelected(event) {
    this.treeViewSelectedTags = [];
    this.dataFilter.tags = this.finalizedTags.length > 1 ? this.finalizedTags.split(',').toString() : this.finalizedTags.toString();
    this.filter();
  }

  get finalizedTags(): string {
    this.finalTagsAlongWithTheCategory = [];
    const categoryAndTags = this.selectedTags;
    this.selectedCategoriesTag = categoryAndTags.filter((tag) => !tag.hasOwnProperty('selectable'));
    for (const selectedTags of this.selectedCategoriesTag) {
      let labelHolder = '';
      labelHolder += selectedTags.label;
      if (selectedTags.parent) {
        labelHolder = selectedTags.parent.label + '__' + labelHolder;
        if (selectedTags.parent?.parent) {
          labelHolder = 'equals:' + selectedTags.parent?.parent?.label + '__' + labelHolder;
          if (selectedTags.parent?.parent?.parent) {
            labelHolder = selectedTags.parent?.parent?.parent?.label + '__' + labelHolder;
          }
        }
      }
      this.finalTagsAlongWithTheCategory.push(labelHolder);
    }
    return this.finalTagsAlongWithTheCategory.toString();
  }

  getExtractedTags(tagWithCategory: string): string {
    const tagArray = tagWithCategory.split('__');
    return tagArray[tagArray.length - 1];
  }

  getExtractedTagsParentSubCategory(tagWithCategory: string): string {
    const tagArray = tagWithCategory.split('__');
    return tagArray[tagArray.length - 2];
  }

  getExtractedTagsParentCategory(tagWithCategory: string): string {
    const tagArray = tagWithCategory.split('__');
    return tagArray[tagArray.length - 3].replace(/equals:/g, '');
  }

  onFilterChangePreapareSelectedTreeNodes() {
    if (this.dataFilter.tags?.length) {
      this.selectedTags = [];
      const tagsWithCategory = this.dataFilter.tags.split(',');
      for (const tag of tagsWithCategory) {
        const pngTreeItem: PNGTree = new PNGTree();
        const tagLength = tag.split('__');
        pngTreeItem.collapsedIcon = 'pi-chevron-right';
        pngTreeItem.expandedIcon = 'pi-chevron-down';
        pngTreeItem.label = this.getExtractedTags(tag);
        pngTreeItem.expanded = true;
        pngTreeItem.parent = {
          label: this.getExtractedTagsParentSubCategory(tag),
          children: [],
          collapsedIcon: 'pi-chevron-right',
          expandedIcon: 'pi-chevron-down',
          expanded: true,
          parent:
            tagLength?.length > 2
              ? {
                  label: this.getExtractedTagsParentCategory(tag),
                  children: [],
                  collapsedIcon: 'pi-chevron-right',
                  expandedIcon: 'pi-chevron-down',
                  expanded: true,
                  parent: undefined
                }
              : undefined
        };
        if (this.groupedCategory) {
          for (const parent of this.groupedCategory.data) {
            for (const children of parent.children) {
              for (const children_data of children.children) {
                if (pngTreeItem.parent.parent.label === parent.label) {
                  (parent['partialSelected'] = true), (parent['selectable'] = false);
                }
                if (pngTreeItem.parent.label === children.label) {
                  (children['partialSelected'] = true), (children['selectable'] = false);
                }
                if (children_data.label === pngTreeItem.label) {
                  (children_data['partialSelected'] = false), this.selectedTags.push(children_data);
                  if (this.selectedTags) {
                    const selctedAllSubCategoy = children.children.every((child) => this.selectedTags.some((item1) => item1.label === child.label));
                    if (selctedAllSubCategoy) {
                      (children['partialSelected'] = false), (children['selectable'] = true);
                      this.selectedTags.push(children);
                    }
                    const selctedAllCategory = parent.children.every((child) => this.selectedTags.some((item1) => item1.label === child.label));
                    if (selctedAllCategory) {
                      (parent['partialSelected'] = false), (parent['selectable'] = true);
                      this.selectedTags.push(parent);
                    }
                  }
                }
              }
            }
          }
        }
      }
      this.cdf.detectChanges();
    }
  }

  // used to get the filter by its id
  getTheFilterById() {
    this.subscriptionManager.add(
      this.projectService.getTheFilterById(this.queryFilterId).subscribe(
        (res) => {
          // if we get some response only then we may try to apply filter
          if (res) {
            this.selectedFilter = res?.data;
            const filterValue = this.sharedFilters?.find((f) => JSON.stringify(f) === JSON.stringify(this.selectedFilter));
            if (filterValue) {
              this.selectedFilterFormControl.setValue(filterValue);
            }
            this.applyFilter();
            this.onFilterChangePreapareSelectedTreeNodes();
          }
        },
        (error) => {
          this.layoutUtilsService.showActionNotification(AppConstants.problemFetchingFilterById, AlertType.Error);
        }
      )
    );
  }

  startDateFilterSelected() {
    this.showCal = true;
    this.startDatePicker.toggle();
  }
  endDateFilterSelected() {
    this.showCal1 = true;
    this.endDatePicker.toggle();
  }
  setDateFilter() {
    this.dateFilter = [
      {
        label: 'Greater than or equal to',
        value: 'gte'
      },
      {
        label: 'Equal to',
        value: 'eq'
      },
      {
        label: 'Less than',
        value: 'lt'
      }
    ];
  }
  ngAfterViewInit() {
    if (this.multiSelectComp) {
      this.multiSelectComp.options = this.statuses;
    }
    if (this.billingTypeSelect) {
      this.billingTypeSelect.options = this.billingType;
    }
  }

  getProjectStatus() {
    this.subscriptionManager.add(
      this.projectService.getProjectStatus().subscribe((res) => {
        this.statuses = res.data?.project_statuses.map((status) => ({
          label: status.project_status.name,
          value: status.project_status.name
        }));
        if (this.dataFilter.statuses) {
          this.selectedStatus = this.dataFilter.statuses.split(',');
        }
        if (this.multiSelectComp) {
          this.multiSelectComp.options = this.statuses;
        }
      })
    );
  }

  getProjectBillingType() {
    this.subscriptionManager.add(
      this.projectService.getBillingType().subscribe((res: any) => {
        res?.billing_types?.forEach((billingType) => {
          this.billingType.push({ label: billingType, value: billingType });
        });
        if (this.dataFilter.billingType) {
          this.selectedBillingType = this.dataFilter?.billingType.split(',');
        }
        if (this.billingTypeSelect) {
          this.billingTypeSelect.options = this.billingType;
        }
      })
    );
  }

  getStoredFilters() {
    const requestObject = {
      // is_shared: true,
      resource: 'projects'
    };
    this.subscriptionManager.add(
      this.clientService.getStoredFilters(requestObject).subscribe(
        (res: ISavedFilterList) => {
          this.loading = false;
          this.sharedFilters = [];
          this.myFilters = [];
          this.availableFilters = res;
          this.filteredFilters = JSON.parse(JSON.stringify(res)); // to deep copy the object
          this.sharedFilters = this.filteredFilters?.data?.query_filters?.filter((q) => q.query_filter.is_shared === true);
          this.myFilters = this.filteredFilters?.data?.query_filters?.filter((q) => q.query_filter.is_shared === false);
          this.cdf.detectChanges();
          this.routerListener();
        },
        () => (this.loading = false)
      )
    );
  }

  onSelectDate(key: string): void {
    if (key === this.startDateKey) {
      this.dataFilter.isStartFromToday = this.dataFilter?.isStartFromToday && this.isToday(this.dataFilter.start_date);
    } else if (key === this.endDateKey) {
      this.dataFilter.isEndFromToday = this.dataFilter?.isEndFromToday && this.isToday(this.dataFilter.end_date);
    }
    this.filter();
  }

  // used to apply filter on the table
  filter(): void {
    this.showCal = false;
    this.showCal1 = false;
    this.filteredFlag = true;
    this.loading = true;
    this.projects = [];
    this.dataFilter.offset = this.appConstants.DEFAULT_PAGE;
    this.dataFilter.limit = this.table._rows;
    this.pageChangeFlag = false;
    this.loadProject(this.table);
  }

  pageChange(): void {
    this.loading = true;
    this.pageChangeFlag = true;
    this.projects = [];
    this.updateEmployeeFilterStatus(this.appConstants.MANAGE_SCREENS.PROJECT);
  }

  sortColumn() {
    this.sortColumnFlag = true;
  }

  filterDate() {
    if (this.dataFilter.position_date) {
      this.filteredFlag = true;
      this.loading = true;
      this.projects = [];
      this.loadProject();
    }
  }

  statusSelected(event) {
    if (event.value.length >= this.statuses.length) {
      this.dataFilter.statuses = null;
    } else {
      this.dataFilter.statuses = event?.value?.toString();
    }
    this.filter();
  }

  billingTypeSelected(event) {
    if (event.value.length >= this.billingType.length) {
      this.dataFilter.billingType = null;
    } else {
      this.dataFilter.billingType = event?.value?.toString();
    }
    this.filter();
  }

  @Debounce()
  loadProject(event?: TableState) {
    let queryFilter: QueryFilterParams = {
      limit: this.pageChangeFlag ? (event?.rows ? event?.rows : this.dataFilter?.limit) : this.dataFilter?.limit ? this.dataFilter.limit : this.appConstants.DEFAULT_ROWS_PER_PAGE,
      offset: this.pageChangeFlag ? (event?.first ? event?.first : this.dataFilter?.offset) : this.dataFilter?.offset ? this.dataFilter.offset : this.appConstants.DEFAULT_PAGE,
      order_by: !this.sortColumnFlag ? (this.dataFilter?.order_by ? this.dataFilter.order_by : this.activeSort(event)) : this.activeSort(event)
    };
    if (!this.pageChangeFlag) {
      this.table._first = this.dataFilter?.offset ? this.dataFilter.offset : event?.first;
      this.table._rows = this.dataFilter?.limit ? this.dataFilter.limit : event?.rows;
    }
    if (!this.sortColumnFlag) {
      this.sortFieldName = this.dataFilter?.order_by ? this.dataFilter.order_by.split(':')[1] : event?.sortField;
      this.sortOrderNumber = this.dataFilter?.order_by ? (this.dataFilter.order_by.split(':')[0] === 'asc' ? 1 : -1) : event?.sortOrder;
    }
    if (this.dataFilter) {
      this.cacheFilter.setCacheFilters({ ...this.dataFilter, ...queryFilter }, this.appConstants.MANAGE_SCREENS.PROJECT);
      if (this.filteredFlag) {
        queryFilter.offset = 0;
        event.first = 0;
        this.filteredFlag = false;
      }

      for (const [key] of Object.entries(this.dataFilter)) {
        if (key === 'name' || key === 'customer') {
          queryFilter[`${key}`] = this.dataFilter[key];
        } else if (key === 'start_date') {
          if (this.dataFilter[key]) {
            const date = this.dataFilter?.isStartFromToday?.toString() === 'true' ? new Date() : this.dataFilter[key];

            if (this.dataFilter?.selectedFilterStartDate === 'gte') {
              queryFilter['start_date_gte'] = this.datePipe.transform(new Date(date), this.appConstants.format);
            } else if (this.dataFilter?.selectedFilterStartDate === 'lt') {
              queryFilter['start_date_lt'] = this.datePipe.transform(new Date(date), this.appConstants.format);
            } else {
              queryFilter[`${key}`] = this.datePipe.transform(new Date(date), this.appConstants.format);
            }
          } else {
            delete queryFilter[`${key}`];
          }
        } else if (key === 'end_date') {
          if (this.dataFilter[key]) {
            const endDate = this.dataFilter?.isEndFromToday?.toString() === 'true' ? new Date() : this.dataFilter[key];

            if (this.dataFilter?.selectedFilterEndDate === 'gte') {
              queryFilter['end_date_gte'] = this.datePipe.transform(new Date(endDate), this.appConstants.format);
            } else if (this.dataFilter?.selectedFilterEndDate === 'lt') {
              queryFilter['end_date_lt'] = this.datePipe.transform(new Date(endDate), this.appConstants.format);
            } else queryFilter[`${key}`] = this.datePipe.transform(new Date(endDate), this.appConstants.format);
          } else {
            delete queryFilter[`${key}`];
          }
        } else {
          if (key !== 'offset' && key !== 'limit' && key !== 'order_by') queryFilter[`${key}`] = this.dataFilter[key];
        }
      }
    }
    delete queryFilter['selectedFilterStartDate'];
    delete queryFilter['selectedFilterEndDate'];
    queryFilter = { ...queryFilter, ...this.extendFiledFilter };
    queryFilter = this.queryStringUtil(queryFilter);
    this.queryFilterGlobal = queryFilter;
    this.getProjectList(queryFilter);
  }

  getMargin(margin) {
    return Math.round(Number(margin) * 100);
  }

  // used to take query string param and removes values which are not set in the query string
  queryStringUtil(queryStringParam: QueryFilterParams) {
    const queryFilter: QueryFilterParams = {};
    if (queryStringParam) {
      for (const [key] of Object.entries(queryStringParam)) {
        if (queryStringParam[key] === '' || queryStringParam[key] === null || queryStringParam[key] === undefined) {
          delete queryStringParam[key];
        }
      }
    }
    return queryStringParam;
  }

  activeSort(event: TableState) {
    if (event.sortField) {
      if (event.sortOrder === 1) {
        return 'asc:' + event.sortField;
      } else {
        return 'desc:' + event.sortField;
      }
    }
    return null;
  }
  onActivityChange(event) {
    const value = event.target.value;
    if (value && value.trim().length) {
      const activity = parseInt(value);

      if (!isNaN(activity)) {
        this.table.filter(activity, 'activity', 'gte');
      }
    }
  }

  // saving the filter, prompting the user to provide the filter group name.
  onSave() {
    const requestObject: SaveFilter = {
      // is_shared: true,
      query_string: this.serialize(this.dataFilter),
      resource: 'projects'
    };
    const dialogTitle = 'Save Project Filter';
    const dialogRef = this.layoutUtilsService.saveClientGroupName(dialogTitle, requestObject);
    dialogRef.afterClosed().subscribe((filtersResponse) => {
      if (filtersResponse) {
        this.getStoredFilters();
        this.layoutUtilsService.showActionNotification('Filter has been saved successfully', AlertType.Success);
      }
    });
  }

  // converts the object in to query param string
  serialize = (obj) => {
    const str = [];
    for (const p in obj) {
      if (obj.hasOwnProperty(p)) {
        str.push(encodeURIComponent(p) + '=' + encodeURIComponent(obj[p]));
      }
    }
    return str.join('&');
  };

  getStatus(status) {
    switch (status) {
      case 'Draft':
        return 'badge badge-draft';
      case 'Forecast':
        return 'badge badge-forecast';
      case 'Booked':
        return 'badge badge-booked';
      case 'Completed':
        return 'badge badge-completed';
    }
  }

  clearFilter(key): void {
    delete this.dataFilter[key];
    if (key === 'start_date') {
      this.dataFilter.isStartFromToday = false;
    } else if (key === 'end_date') {
      this.dataFilter.isEndFromToday = false;
    }
    this.filter();
  }

  showSavedFilterDropDown() {
    this.showSavedFilter = !this.showSavedFilter;
  }

  showFilterOption() {
    this.showFilter = !this.showFilter;
  }

  applySelectedFilterAndUpdateUrl() {
    this.showSavedFilter = false;
    this.selectedFilter = this.selectedFilterFormControl.value;
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: { filterId: this.selectedFilter?.query_filter?.id },
      queryParamsHandling: 'merge'
    });
  }

  // used to convert and object into query param string
  applyFilter() {
    this.selectedStatus = [];
    this.selectedBillingType = [];
    let filter = JSON.parse('{"' + decodeURI(this.selectedFilter.query_filter.query_string).replace(/"/g, '\\"').replace(/&/g, '","').replace(/=/g, '":"') + '"}');
    if (filter.start_date) {
      filter.start_date = new Date(filter.start_date.toString().replace(/%3A/g, ':').replace(/%2B/g, '+'));
    }
    if (filter.end_date) {
      filter.end_date = new Date(filter.end_date.toString().replace(/%3A/g, ':').replace(/%2B/g, '+'));
    }
    if (filter.statuses) {
      filter.statuses = filter.statuses.replace(/%2C/g, ',');
      this.selectedStatus = filter.statuses.split(',');
    }
    if (filter.tags) {
      filter.tags = filter.tags.replace(/%3A/g, ':').replace(/%2C/g, ',');
    }
    if (filter.billingType) {
      filter.billingType = filter.billingType.replace(/%2C/g, ',');
      this.selectedBillingType = filter.billingType.split(',');
    }
    if (filter?.order_by) {
      filter.order_by = filter.order_by.replace(/%3A/g, ':');
    }
    this.dataFilter = filter;
    this.filter();
    this.selectedFilter = null;
    this.filteredFilters.data.query_filters = this.availableFilters.data.query_filters;
  }

  searchFilters(filter: string) {
    this.filteredFilters.data.query_filters = filter.length
      ? this.availableFilters.data.query_filters.filter((availableFilter) => availableFilter.query_filter.name.toLowerCase().includes(filter.toLowerCase()))
      : this.availableFilters.data.query_filters;
    this.sharedFilters = this.filteredFilters?.data?.query_filters.filter((q) => q.query_filter.is_shared === true);
    this.myFilters = this.filteredFilters?.data?.query_filters.filter((q) => q.query_filter.is_shared === false);
  }

  //get No of Weeks
  getNoOfWeeks(position) {
    if (position.start_date && position.end_date) {
      const startDate = new Date(position.start_date);
      const endDate = new Date(position.end_date);
      const difference_In_Time = endDate.getTime() - startDate.getTime();
      const difference_In_Days = difference_In_Time / (1000 * 3600 * 24);
      return Math.ceil(difference_In_Days / 7) + ' Weeks';
    }
  }

  deleteProject() {
    this.isSubmitting = true;
    const projects = this.projects;
    const id = this.deleteProjectId;
    this.subscriptionManager.add(
      this.projectService.deleteProject(id).subscribe(
        () => {
          this.isSubmitting = false;
          this.closeModal();
          this.projects = projects.filter((p) => p.project.id !== id);
          this.totalRecords = this.totalRecords - 1;
          this.cdf.detectChanges();
          this.layoutUtilsService.showActionNotification('Project has been archived successsfully', AlertType.Success);
        },
        () => (this.isSubmitting = true)
      )
    );
  }

  confirmDeleteProject(id: number, isDraftProject: boolean) {
    if (!isDraftProject) return; // since we have blocked user from removing projects which are no longer under Draft status.
    this.deleteProjectId = id;
    this.showDeleteDialog = true;
  }

  clearStartDate(): void {
    delete this.dataFilter.start_date;
    this.dataFilter.isStartFromToday = false;
    this.filter();
  }
  clearEndDate(): void {
    delete this.dataFilter.end_date;
    this.dataFilter.isEndFromToday = false;
    this.filter();
  }

  resetFilters() {
    this.dataFilter = new IFilter();
    this.dataFilter = {
      ...this.dataFilter,
      selectedFilterStartDate: 'gte',
      selectedFilterEndDate: 'gte'
    };
    this.dataFilter.isStartFromToday = this.dataFilter.isEndFromToday = false;
    this.cacheFilter.resetCacheFilters(this.appConstants.MANAGE_SCREENS.PROJECT);
    this.selectedStatus = [];
    this.dataFilter.tags = '';
    this.selectedTags = [];
    this.selectedBillingType = [];
    this.pageChangeFlag = false;
    this.dataFilter.limit = this.appConstants.DEFAULT_ROWS_PER_PAGE;
    this.dataFilter.offset = this.appConstants.DEFAULT_PAGE;

    if (this.table) {
      this.table.first = this.appConstants.DEFAULT_PAGE;
      this.table.rows = this.appConstants.DEFAULT_ROWS_PER_PAGE;
    }
    // reseting the url by removing the filter query string
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: { filterId: null },
      queryParamsHandling: 'merge'
    });
    // reset the filter selection as well
    this.selectedFilterFormControl = new FormControl('');
    this.filter();
  }

  getSymbol(value) {
    return value === 'gte' ? '>=' : value === 'lt' ? '<' : '=';
  }
  editFilter(filter) {
    this.showEditDialog = true;
    this.editFilterObj = _.cloneDeep(filter);
  }

  closeModal() {
    this.deleteProjectId = null;
    this.showDeleteDialog = false;
    this.showEditDialog = false;
    this.editFilterObj = null;
    this.showNameError = false;
    this.showDeleteFilterDialog = false;
    this.deleteFilterObj = null;
    this.showShareDialog = false;
    this.shareFilterObj = null;
  }

  saveEditFilter() {
    if (!this.editFilterObj.query_filter.name.length) {
      this.showNameError = true;
    } else {
      this.isSubmitting = true;
      this.subscriptionManager.add(
        this.clientService.updateFilter(this.editFilterObj.query_filter.id, this.editFilterObj).subscribe(
          (res) => {
            this.layoutUtilsService.showActionNotification(AppConstants.updateFilter, AlertType.Success);
            this.isSubmitting = false;
            this.closeModal();
            this.getStoredFilters();
            this.showSavedFilter = true;
            this.cdf.detectChanges();
          },
          () => (this.isSubmitting = false)
        )
      );
    }
  }

  inputFilterName() {
    this.showNameError = false;
  }

  deleteFilter(filterOption) {
    this.showDeleteFilterDialog = true;
    this.deleteFilterObj = filterOption;
  }

  saveDeleteFilter() {
    this.isSubmitting = true;
    this.subscriptionManager.add(
      this.clientService.deleteStoredFilters(this.deleteFilterObj.query_filter.id).subscribe(
        (res) => {
          this.layoutUtilsService.showActionNotification(AppConstants.deleteFilter, AlertType.Success);
          this.isSubmitting = false;
          this.closeModal();
          this.getStoredFilters();
          this.showSavedFilter = true;
          this.utilizationService.showNewSharedFilter.next('Manage Projects');
          this.cdf.detectChanges();
        },
        () => (this.isSubmitting = false)
      )
    );
  }

  shareFilter(filterOption) {
    this.showShareDialog = true;
    this.shareFilterObj = {
      ...filterOption,
      header: 'Share',
      text: `Do you want to share Filter "${filterOption.query_filter.name}" to all users publically?`
    };
    this.shareFilterObj.query_filter.is_shared = !this.shareFilterObj.query_filter.is_shared;
  }

  copyLinkToTheFilter(filterId: number) {
    const filterHolder = document.createElement('textarea');
    filterHolder.style.position = 'fixed';
    filterHolder.style.left = '0';
    filterHolder.style.top = '0';
    filterHolder.style.opacity = '0';
    // construct the full url to be copied
    // e.g. http://localhost:4200/project/manage?filterId=3
    const hostName = `${window.location.protocol}${window.location.host}`;
    const filterString = `${hostName}${this.router.url.split('?')[0]}/?filterId=${filterId}`;

    filterHolder.value = filterString;
    document.body.appendChild(filterHolder);
    filterHolder.focus();
    filterHolder.select();
    document.execCommand('copy');
    document.body.removeChild(filterHolder);
    this.layoutUtilsService.showActionNotification(AppConstants.filterLinkCopied, AlertType.Success);
  }

  unShareFilter(filterOption) {
    this.showShareDialog = true;
    this.shareFilterObj = {
      ...filterOption,
      header: 'Unshare',
      text: `Do you want to unshare Filter "${filterOption.query_filter.name}" from all users?`
    };
    this.shareFilterObj.query_filter.is_shared = !this.shareFilterObj.query_filter.is_shared;
  }

  saveShareFilter() {
    this.isSubmitting = true;
    this.subscriptionManager.add(
      this.clientService.updateFilter(this.shareFilterObj.query_filter.id, this.shareFilterObj).subscribe(
        (res) => {
          this.layoutUtilsService.showActionNotification(this.shareFilterObj.query_filter.is_shared ? AppConstants.shareFilter : AppConstants.unShareFilter, AlertType.Success);
          this.isSubmitting = false;
          this.closeModal();
          this.getStoredFilters();
          this.showSavedFilter = true;
          this.cdf.detectChanges();
          this.utilizationService.showNewSharedFilter.next('Manage Projects');
        },
        () => (this.isSubmitting = false)
      )
    );
  }

  // managing the permissions. This will update the global_details permissions if there is any change in the local permission enum.
  /**
   * As of right now we are redirecting every user to this manage project screen post login, that is why keeping this manage permission under this component.
   * If for some reason user doesn't have access or if we start redirecting user to some other screen we would have to make this manage permission at some root level in order to make change to user's permission if there are any changes in it.
   * The assumption here is each user would have access to atleast view manage project screen.
   */
  async managePermissions() {
    let currentUser: User;
    if (!this.currentPermissions.length) {
      currentUser = await this.authService.getCurrentUser();
      if (currentUser && currentUser?.user?.permissions) {
        this.currentPermissions = currentUser?.user?.permissions;
      } else {
        this.currentPermissions = [];
      }
    }
    let permission = JSON.parse(JSON.stringify(this.currentPermissions));
    permission = permission[0]?.permissions;
    if (!permission) {
      return;
    }
    const rolesConst = JSON.parse(JSON.stringify(initialPermissions));
    let isSamePermissions = false;
    if (permission?.length !== rolesConst?.length) {
      isSamePermissions = true;
    } else {
      isSamePermissions = this.checkIfLocalAndUserPermissionObjectIsSame(permission, rolesConst);
    }

    this.roleConstToBeUpdated = JSON.parse(JSON.stringify(permission));
    const currentUserPermissionDetails = {
      id: this.currentPermissions[0]?.id,
      name: this.currentPermissions[0]?.name,
      description: this.currentPermissions[0]?.description,
      isDefault: this.currentPermissions[0]?.isDefault
    };
    let global_detail;
    await this.getRolesDetails();
    global_detail = this.getExtendedData(currentUserPermissionDetails);

    if (!isSamePermissions) {
      // since the local permissions are not same as the user's permission list let's update it
      if (this.administrationService.roles?.id) {
        this.administrationService.updateRoles(global_detail, this.administrationService.roles.id).subscribe(() => {});
      }
    }
  }

  getRolesDetails(): Promise<void> {
    return new Promise((resolve) => {
      this.administrationService.getRoles('UserRolePermissions').subscribe((res) => {
        if (res?.data?.global_details) {
          const globalDetail = res?.data?.global_details;
          if (globalDetail && globalDetail[0]?.global_detail?.name === 'UserRolePermissions') {
            this.administrationService.setRoles(globalDetail[0].global_detail);
            resolve();
          }
        }
      });
    });
  }

  getExtendedData(roleValue) {
    const returnData = { name: '', extended_fields: { roles: [] } };
    roleValue = { ...roleValue, permissions: this.roleConstToBeUpdated };
    if (this.administrationService.roles?.id) {
      returnData.name = this.administrationService.roles.name;
      if (this.administrationService.roles && this.administrationService.roles?.extended_fields?.roles?.length) {
        this.administrationService.roles.extended_fields.roles = this.administrationService.roles.extended_fields.roles.filter((role) => role.id !== roleValue.id);
        returnData.extended_fields.roles = this.administrationService.roles.extended_fields.roles;
        returnData.extended_fields.roles.push(roleValue);
      } else {
        returnData.extended_fields.roles = [roleValue];
      }
    } else {
      returnData.name = 'UserRolePermissions';
      returnData.extended_fields.roles = [roleValue];
    }
    return returnData;
  }

  // checks and returns if local/static permission object and user's upstream permission objects are same or not. returns true if same else false
  private checkIfLocalAndUserPermissionObjectIsSame(permission, rolesConst): boolean {
    // they are same so just loop till length and check for the nested feature list length are same or not
    let isSamePermissions = true;
    for (let i = 0; i < permission?.length; i++) {
      if (permission?.[i]?.subfeature?.length === rolesConst?.[i]?.subfeature?.length) {
        // check for any nested subfeatures as well if found any unmatching subfeature update them and make those even--------------------------------------------------------
        for (const subFeat of rolesConst?.[i]?.subfeature) {
          permission?.[i]?.subfeature?.forEach((x) => {
            if (subFeat?.id === x?.id && subFeat?.subfeature?.length) {
              // the difference we get in array will be the one which are new menu item(s). Those we will need to push in user's permission array
              const diffChildPermissionToBeAdded = this.getDifferenceBetweenArray(subFeat?.subfeature, x?.subfeature);
              if (!x.subfeature) {
                x.subfeature = [];
                x.expand = true;
                x.feature = subFeat?.feature;
                // since here we need to add sub feature so check if current key was itself a permission? if so we will remove that as it is now a holder menu item
                if (x.permission) {
                  delete x.module;
                  delete x.permission;
                }
              }
              x.subfeature.push(...diffChildPermissionToBeAdded);
              // the difference we get in array will be the one which menu item(s) have been removed. Those we will need to remove from the user's permission array
              const diffChildPermissionToRremove = this.getDifferenceBetweenArray(x.subfeature, subFeat?.subfeature);
              for (const removingItem of diffChildPermissionToRremove) {
                const indexToBeRemoved = diffChildPermissionToRremove
                  .map(function (item) {
                    return item.Id;
                  })
                  .indexOf(removingItem?.id);
                x?.subfeature?.splice(indexToBeRemoved, 1);
              }
              // update isSamePermissions only if we found any diff
              if (diffChildPermissionToBeAdded?.length || diffChildPermissionToRremove?.length) {
                isSamePermissions = false;
              }
            }
          });
        }
      } else {
        // the difference we get in array will be the one which are new menu item(s). Those we will need to push in user's permission array
        const diffPermissionToBeAdded = this.getDifferenceBetweenArray(rolesConst?.[i]?.subfeature, permission?.[i]?.subfeature);
        permission?.[i]?.subfeature?.push(...diffPermissionToBeAdded);
        // the difference we get in array will be the one which menu item(s) have been removed. Those we will need to remove from the user's permission array
        const diffPermissionToRremove = this.getDifferenceBetweenArray(permission?.[i]?.subfeature, rolesConst?.[i]?.subfeature);
        for (const removingItem of diffPermissionToRremove) {
          const indexToBeRemoved = diffPermissionToRremove
            .map(function (item) {
              return item.Id;
            })
            .indexOf(removingItem?.id);
          permission?.[i]?.subfeature?.splice(indexToBeRemoved, 1);
        }
        // update isSamePermissions only if we found any diff
        if (diffPermissionToBeAdded?.length || diffPermissionToRremove?.length) {
          isSamePermissions = false;
        }
        // check for any nested subfeatures as well if found any unmatching subfeature update them and make those even--------------------------------------------------------
        for (const subFeat of rolesConst?.[i]?.subfeature) {
          permission?.[i]?.subfeature?.forEach((x) => {
            if (subFeat?.name === x.name && subFeat?.subfeature?.length) {
              // the difference we get in array will be the one which are new menu item(s). Those we will need to push in user's permission array
              const diffChildPermissionToBeAdded = this.getDifferenceBetweenArray(subFeat?.subfeature, x?.subfeature);
              x.subfeature.push(...diffChildPermissionToBeAdded);
              // the difference we get in array will be the one which menu item(s) have been removed. Those we will need to remove from the user's permission array
              const diffChildPermissionToRremove = this.getDifferenceBetweenArray(x.subfeature, subFeat?.subfeature);
              for (const removingItem of diffChildPermissionToRremove) {
                const indexToBeRemoved = diffChildPermissionToRremove
                  .map(function (item) {
                    return item.Id;
                  })
                  .indexOf(removingItem?.id);
                x?.subfeature?.splice(indexToBeRemoved, 1);
              }
              isSamePermissions = false;
            }
          });
        }
      }
    }
    return isSamePermissions;
  }

  private getDifferenceBetweenArray(array1, array2) {
    if (!array1) array1 = [];
    if (!array2) array2 = [];

    return array1?.filter((permission1) => {
      return !array2?.some((permission2) => {
        return permission1?.id === permission2?.id;
      });
    });
  }

  onProjectClick(project) {
    this.authService.isPermittedAction([this.permissionModules.MANAGE_PROJECT]).then((isPermitted) => {
      if (isPermitted) {
        this.router.navigate([this.appRoutes.EDIT_PROJECT, project?.project?.id]);
      } else {
        const reportData = {
          projectName: project?.project?.name,
          project_ids: project?.project?.id.toString(),
          start_date_lt: project?.project?.end_date,
          end_date_gte: project?.project?.start_date
        };
        this.cacheFilter.setCacheFilters(reportData, 'positions-report');
        this.router.navigate([this.appRoutes.VIEW_STAFFED_POSITION], { queryParams: { id: project?.project?.id } });
      }
    });
  }

  listChecked(): void {
    this.checkedProject.length ? (this.listSelected = true) : (this.listSelected = false);
  }

  removeProject(): void {
    this.checkedProject = [];
  }

  selectAllProjectCheck(): void {
    this.checkedProject = this.projects;
  }

  updateEmployeeFilterStatus(page: string): void {
    const filter = this.cacheFilter.getCacheFilters(page);
    this.cacheFilter.setCacheFilters({ ...filter, ...{ filterOpen: this.showFilter } }, page);
  }

  checkEmployeeFilterStatus(page: string): void {
    const filter = this.cacheFilter.getCacheFilters(page);
    if (filter?.filterOpen) {
      this.showFilter = filter.filterOpen;
    }
  }

  ngOnDestroy(): void {
    this.updateEmployeeFilterStatus(this.appConstants.MANAGE_SCREENS.PROJECT);
  }

  updateDescriptionPopUPObj(name: string, description): void {
    this.descriptionGlobalName = name;
    this.descriptionGlobal = description;
    this.showdescriptionDialog = true;
    const rows = this.descriptionGlobal?.split('\n').length || 1;
    this.descriptionRows = Math.min(Math.max(rows, 1), 5);
  }

  getGlobalDetailsCategory(): void {
    this.subscriptionManager.add(
      this.adminService.getExtendedFields('ManageExtendedFiled').subscribe(
        (res) => {
          this.loading = false;
          if (res?.data?.global_details) {
            const globalDetail = res?.data?.global_details;
            if (globalDetail && globalDetail[0]?.global_detail?.name === 'ManageExtendedFiled') {
              this.extendFields = res.data.global_details[0].global_detail.extended_fields.extendArray || [];
              this.globalDetailId = res.data.global_details[0].global_detail.id;

              this.frozenCols = [...this.frozenCols, ...this.extractExtendFlow(this.componentType.Project), ...this.extractExtendFlow(this.componentType.Client)];
            }
          }
        },
        () => (this.loading = false)
      )
    );
  }

  updateObjectByPartialKey(partialKey: string, value: any): void {
    // this.dataChangeEvent.next(true);
    const fullKey = `ext_${partialKey?.toLowerCase()?.replace(/\s/g, '_')?.trim()}`;
    this.extendFieldsObj[fullKey] = value || '';
  }

  prepareData(): void {
    const prepareKey = this.extractNames(this.extendFields, this.componentType.Project);
    const prepareObj = {};
    for (const key of prepareKey) {
      const fullKey = `ext_${key?.toLowerCase()?.replace(/\s/g, '_')?.trim()}`;
      if (key) {
        prepareObj[fullKey] = this.extendFieldsObj?.hasOwnProperty(fullKey) ? this.extendFieldsObj[fullKey] : '';
      }
    }
    this.extendFieldsObj = { ...this.extendFieldsObj, ...prepareObj };
  }
  extractNames(data: any, componentName: string): string[] {
    return data
      ?.map((item) => {
        const projectConfig = item?.jsonData?.extendedFieldsConfig?.find((config) => config?.component === componentName);
        return projectConfig ? projectConfig?.fields.map((field) => field?.name?.trim()) : [];
      })
      .flat(2);
  }

  // extractExtendFlow(): Array<any> {
  //   let extendFiled = [];
  //   let prepareKey = this.extractNames(this.extendFields, this.componentType.Project);
  //   for (const key of prepareKey) {
  //     extendFiled.push({ field: key?.replace(/\s/g, "_")?.trim() || '', monthLabel: key?.toUpperCase() })
  //   }
  //   return extendFiled;
  // }

  extractExtendFlow(componentType: ComponentsType): Array<any> {
    let extendFiled = [];
    let prepareKey = this.extractNames(this.extendFields, componentType);
    for (const key of prepareKey) {
      const keyPreProcess = `${componentType}: ${key?.replace(/\s/g, '_').trim()}`.trim();
      extendFiled.push({
        field: keyPreProcess || '',
        monthLabel: `${componentType}: ${key?.toUpperCase()}`,
        bydefaultSelected: false
      });
    }
    return extendFiled;
  }

  openExtendFiledPopup(projectObj: Project, fieldDetail: extendedField, component?: string): void {
    this.fieldDetail = fieldDetail;
    this.linkValue = this.getValueBYExtendFiled(ComponentsType[component], projectObj, fieldDetail?.DBTag, fieldDetail?.type) || '';
    if (fieldDetail?.type === this.filedType.Hyperlink && this.linkValue) {
      this.openHyperlinkPopup(projectObj, fieldDetail?.name);
      return;
    }
    this.updateExtendFiled = fieldDetail?.name;
    this.showUpdateExtendFiledDialog = true;
    this.projectObj = projectObj;
    this.projectId = projectObj.project.id;
    this.projectSetupForm = JSON.parse(JSON.stringify(this.projectObj));
    this.showHyperlinkNavigationDialog = false;
  }

  openHyperlinkPopup(projectObj: Project, fieldName?: string): void {
    this.showHyperlinkNavigationDialog = true;
    this.updateExtendFiled = fieldName;
    this.projectObj = projectObj;
  }

  closeExtendFiledPopup(): void {
    this.projects = this.projects.map((project) => {
      if (project.project.id === this.projectId) {
        return {
          project: {
            ...project.project,
            extended_fields: { ...this.projectSetupForm?.project?.extended_fields }
          }
        };
      }
      return project;
    });

    this.updateExtendFiled = '';
    this.showUpdateExtendFiledDialog = false;
    this.projectObj = {};
    this.fieldDetail = null;
    this.extendedFieldFormComponent?.onReset();
  }

  saveProject(): void {
    this.createProjectSetup();
  }
  createProjectSetup(): void {
    if (JSON.stringify(this.projectSetupForm.project?.extended_fields) !== JSON.stringify(this.projectObj.project?.extended_fields)) {
      this.isSubmitting = true;
      this.service = 'editProjectSetup';
      const finalProject = this.processUpdateObj(this.projectObj.project);
      this.subscriptionManager.add(
        this.projectService[this.service](finalProject, this.projectId).subscribe(
          (res) => {
            this.layoutUtilsService.showActionNotification('Project updated successfully', AlertType.Success);
            this.getProjectList(this.queryFilterGlobal);
            this.closeExtendFiledPopup();
            this.isSubmitting = false;
            this.fieldDetail = null;
            this.extendedFieldFormComponent?.onReset();
          },
          (err) => {
            this.layoutUtilsService.showActionNotification(err.Message, AlertType.Error);
          }
        )
      );
    } else {
      this.closeExtendFiledPopup();
    }
  }
  getProjectList(queryFilter: any): void {
    this.subscriptionManager.add(
      this.projectService.getProjectData(queryFilter).subscribe(
        (res: HttpResponse<ProjectList>) => {
          this.projects = res.body.data.projects;
          this.cdf.detectChanges();
          this.totalRecords = Number(res.headers.get('x-total-count'));
          this.loading = false;
          this.showPaginator = this.totalRecords <= 10 ? false : true;
          this.ktDialogService.hide();
          this.cdf.detectChanges();
          this.isSubmitting = false;
          if (this.showUpdateExtendFiledDialog) {
            this.closeExtendFiledPopup();
          }
        },
        () => {
          this.loading = false;
          this.isSubmitting = false;
          this.ktDialogService.hide();
          this.cdf.detectChanges();
        }
      )
    );
  }

  processUpdateObj(projectSetupForm) {
    return {
      // id: this.projectId,
      // description: projectSetupForm.description,
      // name: projectSetupForm.name,
      // status: projectSetupForm.status,
      // region_id: 1,
      extended_fields: projectSetupForm?.extended_fields || {}
      // amount: projectSetupForm.billing_type === this.billingTypes.TIME_MATERIALS ? null : projectSetupForm.amount,
      // billing_type: projectSetupForm.billing_type,
      // customer_id: projectSetupForm.customer.id,
    };
  }

  getValueByPartialKey(dbTag: string, extendFieldsObj: any = {}): string {
    return extendFieldsObj?.hasOwnProperty(dbTag) ? extendFieldsObj[dbTag] : '';
  }
  filterExtendFiled(dbTag: string, event: any): void {
    if (event?.value) {
      this.extendFiledFilter[dbTag] = `${event?.value?.trim()}` as string;
    } else {
      this.deleteExtendFiledFilter(dbTag);
    }
    this.filter();
  }
  deleteExtendFiledFilter(dbTag): void {
    if (this.extendFiledFilter?.hasOwnProperty(dbTag)) {
      delete this.extendFiledFilter[dbTag];
    }
    this.filter();
  }

  getFilterValue(dbTag: string): string {
    return this.extendFiledFilter?.hasOwnProperty(dbTag) ? this.extendFiledFilter[dbTag] : ('' as string);
  }

  copyProject(projectId: number): void {
    this.service = this.appConstants.COPY_PROJECT;
    this.subscriptionManager.add(
      this.projectService[this.service](projectId).subscribe(
        (res) => {
          this.layoutUtilsService.showActionNotification(this.appConstants.copyProject, AlertType.Success);
          this.getProjectList(this.queryFilterGlobal);
          if (res?.data?.project?.id) {
            this.router.navigate([this.appRoutes.EDIT_PROJECT, res?.data?.project?.id], {
              queryParams: { copy: true }
            });
          }
          this.isSubmitting = false;
        },
        (err) => {
          this.layoutUtilsService.showActionNotification(err.Message, AlertType.Error);
          this.isSubmitting = false;
        }
      )
    );
  }

  getValueBYExtendFiled(componentsType: ComponentsType, object: any, dbTag: string, fieldType: string = ''): string {
    let extendedOBJ = {};
    switch (componentsType) {
      case ComponentsType.Project:
        extendedOBJ = object?.project.extended_fields || {};
        break;
      case ComponentsType.Client:
        extendedOBJ = object?.project.customer.extended_fields || {};
        break;
    }
    return this.getValueByDBTag(dbTag, extendedOBJ, fieldType);
  }

  getValueByDBTag(dbTag: string, extendFieldsObj: any = {}, fieldType: string = ''): string {
    if (fieldType === this.filedType.MultiDropdown && extendFieldsObj?.hasOwnProperty(dbTag)) {
      const value = Array.isArray(extendFieldsObj[dbTag]) ? extendFieldsObj[dbTag]?.map((item) => item?.name).join(', ') : '';
      return value;
    }
    if (fieldType === this.filedType.Dropdown && extendFieldsObj?.hasOwnProperty(dbTag)) {
      return extendFieldsObj?.hasOwnProperty(dbTag) ? extendFieldsObj[dbTag].name : '';
    }
    return extendFieldsObj?.hasOwnProperty(dbTag) ? extendFieldsObj[dbTag] : '';
  }

  checkSelectedColumn(componentType: ComponentsType, key: string): boolean {
    const finalKey = `${componentType}: ${key?.replace(/\s/g, '_')?.trim()}`.trim() || '';
    return this._pCols.includes(finalKey);
  }

  isToday(date: any): boolean {
    if (!date) return false;
    const today = new Date();
    return date.getFullYear() === today.getFullYear() && date.getMonth() === today.getMonth() && date.getDate() === today.getDate();
  }

  getFormattedFilterDate(key: string): string | Date {
    if (key === this.startDateKey) {
      return this.dataFilter?.isStartFromToday?.toString() === 'true' ? 'Today' : this.dataFilter?.start_date ? new Date(this.dataFilter?.start_date).toLocaleDateString() : '';
    } else if (key === this.endDateKey) {
      return this.dataFilter?.isEndFromToday?.toString() === 'true' ? 'Today' : this.dataFilter?.end_date ? new Date(this.dataFilter?.end_date).toLocaleDateString() : '';
    }
  }

  onTodayClicked(key: string): void {
    if (key === this.startDateKey) {
      this.dataFilter.isStartFromToday = true;
    } else if (key === this.endDateKey) {
      this.dataFilter.isEndFromToday = true;
    }
  }

  onClearDateFilter(key: string): void {
    if (key === this.startDateKey) {
      this.clearFilter('start_date');
      this.clearFilter('selectedFilterStartDate');
    } else if (key === this.endDateKey) {
      this.clearFilter('end_date');
      this.clearFilter('selectedFilterEndDate');
    }
  }

  getValidLink(link: string): string {
    if (!link) return '';
    return this.appConstants.regexForHyperlink.test(link) ? link : `https://${link}`;
  }

  getLinkForNavigation(componentsType: ComponentsType, fieldObject: Project, dbTag: string): string {
    const link = this.getValueBYExtendFiled(componentsType, fieldObject, dbTag);
    if (!link) return '';
    return this.appConstants.regexForHyperlink.test(link) ? link : `https://${link}`;
  }

  checkFormValidity(): boolean {
    const isValid = this.extendedFieldFormComponent?.isFormValid() || false;
    return !isValid;
  }

  navigateOnLink(): void {
    this.showHyperlinkNavigationDialog = false;
    this.fieldDetail = null;
    if (this.linkValue) {
      const hyperLink = this.getValidLink(this.linkValue);
      window.open(hyperLink, '_blank');
      this.linkValue = '';
    }
  }
}
