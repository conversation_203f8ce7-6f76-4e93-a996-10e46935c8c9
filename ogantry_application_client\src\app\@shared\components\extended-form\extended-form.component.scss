#extended-form {
  .card-body.create-card {
    box-shadow: none;
    margin: 0;
    padding: 20px !important;
  }

  .special-fix {
    right: 30px;
    bottom: 10px;
    position: fixed;
  }

  .mhh-90 {
    height: 71vh !important;
    overflow: auto !important;
  }
}

::ng-deep .extended-dropdown .p-dropdown,
::ng-deep .extended-multiselect .p-multiselect {
  width: 100%;
  height: 100%;
  border-radius: 9px !important;
  border: none !important;
  background-color: #f8f8ff !important;
  min-height: 60px !important;
  padding-top: 1rem;
  display: flex;
  align-items: baseline;
}

::ng-deep .extended-dropdown .p-dropdown .p-dropdown-label,
::ng-deep .extended-multiselect .p-multiselect .p-multiselect-label {
  color: #495057;
  font-family: Poppins;
  font-size: 16px;
  line-height: 25px;
  padding-left: 1rem;
}

::ng-deep .extended-dropdown .p-dropdown .p-dropdown-label.p-placeholder,
::ng-deep .extended-multiselect .p-multiselect .p-multiselect-label.p-placeholder {
  color: #b5b5c3 !important;
}
