export interface ManageTimeApiResponse {
  data: {
    positions: Position[];
  };
  message: string;
  success: boolean;
}

export interface Position {
  position: {
    bill_rate: string;
    cost: string;
    daily_billable_hours: string;
    deleted_date: null | string;
    effective_end_date: null | string;
    effective_start_date: string;
    employee: Employee;
    end_date: string;
    id: number;
    name: string;
    pid: number;
    project: Project;
    start_date: string;
    tags: string[];
    time_entries: TimeEntry[];
    type: string;
    visibility: string;
    edit?: boolean;
  };
}

export interface Employee {
  first_name: string;
  id: number;
  last_name: string;
}

export interface Project {
  billing_type: string;
  customer: Customer;
  id: number;
  name: string;
}

export interface Customer {
  id: number;
  name: string;
}

export interface TimeEntry {
  time_entry: {
    date: string;
    hours: string;
    id?: number;
    position_id: number;
    comment?: string | null;
    status?: string;
  };
}

interface MyObject {
  [key: string]: string;
}

export interface EmployeeData {
  data: {
    employees: Employee[];
  };
}

export interface Employee {
  employee: {
    id: number;
    name: string;
  };
}

export interface EditComment {
  index: number;
  day: any;
  hours?: number;
  comment?: string;
  time_entry?: string;
}

export interface TimeSheetDayObj {
  label: string;
  fullDate: string | Date;
}
