import { FormControl } from '@angular/forms';
import { GlobalDetailSubCategory, GlobalDetailTaggingCategory, GlobalDetailTags, QueryFilter, SubCategory, TagCategory } from './../../administration/administration.model';
import { FINANCIAL_REVIEW_TYPES } from './../../project/financial-review/financial-review.model';
import { DatePipe, KeyValue } from '@angular/common';
import { AfterViewInit, ChangeDetectorRef, Component, HostListener, Input, OnInit, ViewChild } from '@angular/core';
import { MatSidenav } from '@angular/material/sidenav';
import { MONTH_NAMES, TableHeader } from '@entities/project/financial-review/financial-review.model';
import { BillingTypes, Project, ProjectList, ValidMonthlyProjection } from '@entities/project/project.model';
import { ProjectService } from '@entities/project/project.service';
import { FilterReport, Group, IFilter, QueryFilterParams } from '@entities/utilization-management/utilization.model';
import { UtilizationService } from '@entities/utilization-management/utilization.service';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { ButtonParams } from '@shared/models';
import { SidebarParams } from '@shared/models/sidebar-params.model';
import { KtDialogService } from '@shared/services';
import { TreeNode } from 'primeng/api';
import moment from 'moment';
import { LayoutConfigService } from '@shared/services/layout-config.service';
import { AppConstants } from '@shared/constants';
import { ISavedFilterList, SaveFilter } from '@entities/administration/administration.model';
import { AlertType } from '@shared/models/alert-type.enum';
import { LayoutUtilsService } from '@shared/services/layout-utils.service';
import { GetSetCacheFiltersService } from '@shared/services/get-set-cache-filters.service';
import { BehaviorSubject, iif, interval, Observable, of, Subscription, timer, forkJoin } from 'rxjs';
import { retry, delay, retryWhen, map, tap, delayWhen, take, switchMap, catchError } from 'rxjs/operators';
import { throwError } from 'rxjs';
import * as _ from 'lodash';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { AdministrationService } from '@entities/administration/administration.service';
import { PNGTree, TreeViewStructure } from '@entities/administration/append-tags/tree-view-model';
import { TreeNode as TagsTreeNode } from '@entities/administration/append-tags/tree-view-model';
import { AddCommasToNumbersPipe } from '@shared/pipes/add-commas-to-numbers.pipe';

@Component({
  selector: 'app-manage-pl',
  templateUrl: './manage-pl.component.html',
  styleUrls: ['./manage-pl.component.scss']
})
export class ManagePLComponent extends SflBaseComponent implements OnInit, AfterViewInit {
  openFilter = false;
  cardTitle = 'Manage P&L';
  cardSubTitle = null;
  buttons: ButtonParams[] = [
    {
      btnSvg: 'download-wt',
      btnClass: 'btn-filter-icon download',
      action: this.openExportOptionList.bind(this)
    }
  ];
  splitButtonDropDownOption = {
    action: this.openSideBar.bind(this),
    options: [
      {
        label: 'Get Stored Filters',
        icon: 'get-stored-filter-split-button-icon',
        command: () => {
          this.openSaveFilterList();
        }
      },
      {
        label: 'Save Filter',
        icon: 'save-filter-split-button-icon',
        command: () => {
          this.onSaveFilter();
        }
      },
      {
        label: 'Reset Filter',
        icon: 'reset-filter-split-button-icon',
        command: () => {
          this.resetFilter();
        }
      }
    ]
  };
  sidebarButtons: ButtonParams[] = [
    {
      btnSvg: 'filter-list',
      btnClass: 'btn-filter-icon',
      action: this.openSaveFilterList.bind(this)
    },
    {
      btnSvg: 'save',
      btnClass: 'btn btn-sm btn-icon btn-icon-light svg-icon svg-icon-md icon-background mr-2 filter-btn-wrapper',
      action: this.onSaveFilter.bind(this)
    }
  ];
  exportButtons: ButtonParams[] = [
    {
      action: this.exportReport.bind(this)
    }
  ];

  tags = [];

  sidebarParams: SidebarParams<FilterReport>;
  @ViewChild('sidebarFilter', { static: true }) el: MatSidenav;

  //declarations to generate data
  finalProjectionData: TreeNode[] = [];

  projections: any;
  @Input() projectId?: number;
  @Input() project: Project;

  tableHeaders: TableHeader[] = [];
  clientGroups;
  projectGroups;
  projectsRevenue = [];
  projectsExpense = [];
  bench = [];
  workExceptions = [];
  fixedRevenueTreeData: TreeNode[] = [];
  tAndMRevenueTreeData: TreeNode[] = [];
  revenueTreeData: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.REVENUE },
    children: []
  };
  expenseTreeData: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.EXPENSE },
    children: []
  };
  grossProfitTreeData: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.GROSS_PROFIT },
    children: []
  };
  grossMarginTreeData: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.GROSS_MARGIN },
    children: []
  };
  benchTreeData: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.BENCH },
    children: []
  };
  projectExpenseTreeData: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.PROJECTS },
    children: []
  };
  WorkExpenseTreeData: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.WORK_EXCEPTION },
    children: []
  };
  pLAdjustData: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.PL_ADJUST }
  };
  cOGSAdjust: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.COGS_ADJUST }
  };
  sgaAdjustData: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.SGA_ADJUST }
  };
  netProfit: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.NET_PROFIT }
  };
  netMargin: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.NET_MARGIN }
  };
  peopleExpenseTreeData: TreeNode[] = [];
  grossProfitData: TreeNode[] = [];
  grossMarginData: TreeNode[] = [];
  dataFilter: IFilter = new IFilter();
  grossProfitTotal: number;
  revenueTotal: number;
  statuses = [];
  defaultStatuses: string[];
  frozenCols = [];
  height = 'calc((var(--fixed-content-height, 1vh) * 100) - 100px)';
  resizeFlag = false;
  showFilterListDialog = false;
  availableFilters = null;
  selectedFilter = null;
  client = [];
  activeEmployee = [];
  loadingEmp = false;
  showAdjustValuesDialog = false;
  adjustValueObj = null;
  showAmtError = false;
  showNoteError = false;
  // Multiple S&GA update properties
  showMultipleSgaDialog = false;
  sgaPlugs = [];
  sgaPlugsLoading$ = new BehaviorSubject<boolean>(false);
  isMultipleSgaSubmitting = false;
  showMultipleSgaError = false;
  showMultipleSgaAmountError = false;
  sgaMonthOptions = [];
  selectedSgaMonths = [];
  multipleSgaAmount: number;
  multipleSgaReason: string;
  projects = [];
  loading$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  sharedFilters: QueryFilter[] = [];
  myFilters: QueryFilter[] = [];
  showSavedFilter = false;
  selectedFilterFormControl = new FormControl('');
  editFilterObj: QueryFilter;
  showNameError = false;
  showDeleteDialog = false;
  deleteFilterObj: QueryFilter;
  showShareDialog = false;
  shareFilterObj = null;
  showEditDialog = false;
  filteredFilters: ISavedFilterList;
  queryFilterId: number;
  showExportOptionDialog: boolean;
  showExportOptions: boolean;
  tagCategories: TagCategory[] = [];
  taggingTags = [];
  globalDetailsTaggingCategory: GlobalDetailTaggingCategory;
  tagSubCategory: SubCategory[] = [];
  globalDetailsTagSubCategory: GlobalDetailSubCategory;
  globalDetailsTag: GlobalDetailTags;
  groupedCategory: TreeViewStructure;
  selectedTags = [];
  selectedCategoriesTag: PNGTree[] = [];
  finalTagsAlongWithTheCategory: string[] = [];
  costConstant = 'cost';
  showPausedProjectDialog = false;
  pausedProjectList: Project[] = [];
  isResumeValidationInProgress = false;
  private messageInterval: Subscription;
  private currentMessageIndex = 0;
  rotatingMessage = '';
  calculatingProjectName = '';
  addCommasToNumbersPipe = new AddCommasToNumbersPipe();

  constructor(
    private readonly projectService: ProjectService,
    readonly utilizationService: UtilizationService,
    private readonly cdf: ChangeDetectorRef,
    readonly datePipe: DatePipe,
    private readonly ktDialogService: KtDialogService,
    private readonly layoutConfigService: LayoutConfigService,
    private readonly layoutUtilsService: LayoutUtilsService,
    private readonly cacheFilter: GetSetCacheFiltersService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly router: Router,
    private readonly adminService: AdministrationService
  ) {
    super();
  }

  ngOnInit() {
    if (window.innerWidth <= 1024) {
      this.resizeFlag = true;
    } else {
      this.resizeFlag = false;
    }
    if (this.cacheFilter.getCacheFilters('PL')) {
      this.dataFilter = this.cacheFilter.getCacheFilters('PL');
    }
    this.applyAllFilter();
    this.frozenCols = [{ field: 'type', monthLabel: 'Type' }];
    this.getClientGroup();
    this.getProjectGroup();
    this.getProjectList();
    this.getProjectStatus();
    this.getStoredFilters();
    this.getClient();
    this.getCategoryMasterData();
  }

  async ngAfterViewInit() {
    await this.routerListener();
    if (this.queryFilterId) {
      this.openFilter = false;
    }
    if (this.el) {
      this.loading$.next(false);
      this.openFilter = true;
    }
  }

  openExportOptionList() {
    this.showExportOptionDialog = true;
    this.showExportOptions = true;
    this.cdf.detectChanges();
  }

  getProjectList() {
    this.subscriptionManager.add(
      this.utilizationService.getProjectList().subscribe((res) => {
        const projects = res.data.projects.map((project) => ({
          label: `${project.project?.customer?.name} : ${project?.project?.name}`,
          value: { name: project?.project?.name, value: String(project?.project?.id) }
        }));
        this.projects = projects;
        this.sortList(this.projects);
        this.applyAllFilter();
        this.cdf.detectChanges();
      })
    );

    this.subscriptionManager.add(
      this.utilizationService.getClientData().subscribe((res) => {
        const client = res?.body?.data?.customers?.map((c) => ({
          label: c.customer.name,
          value: { name: c.customer.name, value: String(c.customer.id) }
        }));
        this.client = client;
        this.cdf.detectChanges();
      })
    );
  }

  defaultFilters() {
    if (!this.dataFilter?.statuses) {
      this.dataFilter.statuses = this.defaultStatuses.toString();
      this.dataFilter.status = this.defaultStatuses;
      this.tags.push({
        label: 'Project Status',
        value: this.dataFilter.statuses
      });
    }
    if (this.dataFilter.include_utilizations === null || this.dataFilter.include_utilizations === undefined) {
      this.dataFilter.include_utilizations = true;
      this.tags.push({
        label: 'Include Bench',
        value: 'True',
        key: ['include_utilizations']
      });
    }
    if (this.dataFilter.include_work_exceptions === null || this.dataFilter.include_work_exceptions === undefined) {
      this.dataFilter.include_work_exceptions = true;
      this.tags.push({
        label: 'Include Work Exceptions',
        value: 'True',
        key: ['include_work_exceptions']
      });
    }
    if (this.dataFilter.include_pl_plugs === null || this.dataFilter.include_pl_plugs === undefined) {
      this.dataFilter.include_pl_plugs = true;
      this.tags.push({
        label: 'Include P&L Plugs',
        value: 'True',
        key: ['include_pl_plugs']
      });
    }

    if (!this.dataFilter.start_date && !this.dataFilter.end_date) {
      this.dataFilter.rollingOption = 'Current plus 2 months';
      if (this.dataFilter.rollingOption) {
        this.tags.push({
          label: 'Rolling',
          value: this.dataFilter.rollingOption.toString(),
          key: ['start_date', 'end_date', 'rollingOption']
        });
      }
    }
  }

  @HostListener('window:resize', ['$event'])
  onResize(event) {
    if (event.target.innerWidth <= 1024) {
      this.resizeFlag = true;
    } else {
      this.resizeFlag = false;
    }
  }

  getCategoryMasterData() {
    this.tagCategories = [];
    this.loading$.next(true);
    this.subscriptionManager.add(
      this.adminService.getTagCategories('TagCategoryManagement').subscribe(
        (res) => {
          this.loading$.next(false);
          if (res?.data?.global_details) {
            const globalDetail = res?.data?.global_details;
            if (globalDetail && globalDetail[0]?.global_detail?.name === 'TagCategoryManagement') {
              this.globalDetailsTaggingCategory = globalDetail[0];
              this.tagCategories = globalDetail[0].global_detail.extended_fields.tagCategory;
              this.adminService.setTagCategories(globalDetail[0].global_detail);
              this.getTagSubCategories();
            }
          }
        },
        () => this.loading$.next(false)
      )
    );
  }

  getTagSubCategories() {
    this.tagSubCategory = [];
    this.loading$.next(true);
    this.subscriptionManager.add(
      this.adminService.getTagSubCategories('SubCategoryManagement').subscribe(
        (res) => {
          this.loading$.next(false);
          if (res?.data?.global_details) {
            const globalDetail = res?.data?.global_details;
            if (globalDetail && globalDetail[0]?.global_detail?.name === 'SubCategoryManagement') {
              this.globalDetailsTagSubCategory = globalDetail[0];
              this.tagSubCategory = globalDetail[0].global_detail.extended_fields.subCategory;
              this.adminService.setTagSubCategories(globalDetail[0].global_detail);
              this.getGlobalDetailTags();
            }
          }
        },
        () => this.loading$.next(false)
      )
    );
  }

  getGlobalDetailTags() {
    this.taggingTags = [];
    this.loading$.next(true);
    this.subscriptionManager.add(
      this.adminService.getTags('TagManagement').subscribe(
        (res) => {
          this.loading$.next(false);
          if (res?.data?.global_details) {
            const globalDetail = res?.data?.global_details;
            if (globalDetail && globalDetail[0]?.global_detail?.name === 'TagManagement') {
              this.globalDetailsTag = globalDetail[0];
              this.taggingTags = globalDetail[0].global_detail.extended_fields.tags;
              this.adminService.setTags(globalDetail[0].global_detail);
              this.combineCategoryAndSubCategory();
            }
          }
        },
        () => this.loading$.next(false)
      )
    );
  }

  combineCategoryAndSubCategory() {
    for (const category of this.globalDetailsTaggingCategory.global_detail.extended_fields.tagCategory) {
      category.subTagCategory = [...this.tagSubCategory?.filter((subCate) => subCate?.parentCategoryId === category?.id)];
    }
    this.injectTagsToRespectiveCategoryOrSubCategory();
    this.initGroupingCategoryTags();
  }

  injectTagsToRespectiveCategoryOrSubCategory() {
    for (const category of this.globalDetailsTaggingCategory.global_detail.extended_fields.tagCategory) {
      for (const tag of this.taggingTags) {
        if (tag.tagCategory === category.id) {
          const subCateIndex = category.subTagCategory.findIndex((subCate) => subCate.id === tag.subTagCategory);
          if (subCateIndex !== -1) {
            category.subTagCategory[subCateIndex]['tags'].push(tag);
          } else {
            category.tags.push(tag);
          }
        }
      }
      category.subTagCategory = [...this.tagSubCategory?.filter((subCate) => subCate?.parentCategoryId === category?.id)];
    }
  }

  initGroupingCategoryTags() {
    this.groupedCategory = { data: [] };
    for (const [index, category] of this.globalDetailsTaggingCategory.global_detail.extended_fields.tagCategory.entries()) {
      const dataCollection: TagsTreeNode = new TagsTreeNode();
      dataCollection.label = category.name;
      dataCollection.selectable = false;
      dataCollection.collapsedIcon = 'pi-chevron-right';
      dataCollection.expandedIcon = 'pi-chevron-down';
      dataCollection.expanded = true;
      if (category?.subTagCategory?.length) {
        for (const [subIndex, subCate] of category?.subTagCategory?.entries()) {
          dataCollection.children.push({
            label: subCate.name,
            collapsedIcon: 'pi-chevron-right',
            expandedIcon: 'pi-chevron-down',
            children: [],
            selectable: false,
            expanded: true
          });
          if (subCate?.tags?.length) {
            for (const [tagIndex, tag] of subCate?.tags?.entries()) {
              dataCollection.children[subIndex]?.children?.push({ label: tag.name, collapsedIcon: 'pi-chevron-right', expandedIcon: 'pi-chevron-down', expanded: true });
            }
          }
        }
      }
      if (category?.tags?.length) {
        for (const [tagIndex, tag] of category?.tags?.entries()) {
          dataCollection.children?.push({ label: tag.name, collapsedIcon: 'pi-chevron-right', expandedIcon: 'pi-chevron-down', expanded: true });
        }
      }
      this.groupedCategory.data.push(dataCollection);
    }
    const data = document.getElementById('project-status');
    data.click();
    this.onFilterChangePreapareSelectedTreeNodes();
  }

  getExtractedTags(tagWithCategory: string): string {
    const tagArray = tagWithCategory.split('__');
    return tagArray[tagArray.length - 1];
  }

  onFilterChangePreapareSelectedTreeNodes() {
    if (this.dataFilter.tags?.length) {
      this.selectedTags = [];
      const tagsWithCategory = this.dataFilter.tags.split(',');
      for (const tag of tagsWithCategory) {
        const pngTreeItem: PNGTree = new PNGTree();
        const tagLength = tag.split('__');
        pngTreeItem.collapsedIcon = 'pi-chevron-right';
        pngTreeItem.expandedIcon = 'pi-chevron-down';
        pngTreeItem.label = this.getExtractedTags(tag);
        pngTreeItem.expanded = true;
        (pngTreeItem.partialSelected = false),
          (pngTreeItem.selectable = false),
          (pngTreeItem.parent = {
            label: this.getExtractedTagsParentCategory(tag),
            children: [],
            collapsedIcon: 'pi-chevron-right',
            expandedIcon: 'pi-chevron-down',
            expanded: true,
            parent:
              tagLength?.length > 2
                ? {
                    label: this.getExtractedTagsParentCategory(tag),
                    children: [],
                    collapsedIcon: 'pi-chevron-right',
                    expandedIcon: 'pi-chevron-down',
                    expanded: true,
                    parent: undefined
                  }
                : undefined
          });
        if (this.groupedCategory) {
          for (const parent of this.groupedCategory.data) {
            for (const children of parent.children) {
              for (const children_data of children.children) {
                if (children_data.label === pngTreeItem.label) {
                  this.selectedTags.push(children_data);
                }
              }
            }
          }
        }
      }
      this.cdf.detectChanges();
    }
  }

  getExtractedTagsParentCategory(tagWithCategory: string): string {
    const tagArray = tagWithCategory.split('__');
    return tagArray[tagArray.length - 2];
  }

  getProjectStatus() {
    this.subscriptionManager.add(
      this.projectService.getProjectStatus().subscribe((res) => {
        const projectStatuses = res.data?.project_statuses || [];
        this.defaultStatuses = projectStatuses.filter((status) => status.project_status.is_default).map((status) => status.project_status.name);

        this.statuses = projectStatuses.map((status) => ({
          label: status.project_status.name,
          value: status.project_status.name
        }));
        this.defaultFilters();
      })
    );
  }

  getClientGroup() {
    const requestObject = {
      // is_shared: true,
      resource: 'customers'
    };

    this.subscriptionManager.add(
      this.utilizationService.getGroup(requestObject).subscribe((res) => {
        const response = JSON.parse(JSON.stringify(res));
        const clientGrps = response.data.query_filters?.map((query) => ({
          label: query.query_filter.name,
          value: { name: query.query_filter.name, value: query.query_filter.query_string }
        }));
        this.clientGroups = clientGrps;
        this.cdf.detectChanges();
      })
    );
  }

  getClient() {
    this.subscriptionManager.add(
      this.utilizationService.getClientData().subscribe((res) => {
        const client = res?.body?.data?.customers?.map((c) => ({
          label: c.customer.name,
          value: { name: c.customer.name, value: String(c.customer.id) }
        }));
        this.client = client;
        this.cdf.detectChanges();
      })
    );
  }

  // used to sort the given list in alphabetical order
  sortList(sortList) {
    if (this.sortList.length > 0) {
      sortList.sort((a, b) => {
        const fa = a?.label?.toLowerCase();
        const fb = b?.label?.toLowerCase();
        if (fa < fb) {
          return -1;
        }
        if (fa > fb) {
          return 1;
        }
        return 0;
      });
    }
  }

  getProjectGroup() {
    const requestObject = {
      // is_shared: true,
      resource: 'projects'
    };

    this.subscriptionManager.add(
      this.utilizationService.getGroup(requestObject).subscribe((res) => {
        const response = JSON.parse(JSON.stringify(res));
        const projectGrps = response.data.query_filters?.map((query) => ({
          label: query.query_filter.name,
          value: { name: query.query_filter.name, value: query.query_filter.query_string }
        }));
        this.projectGroups = projectGrps;
        this.cdf.detectChanges();
      })
    );
  }

  getClientsIds() {
    const paramsToRemove = ['offset', 'limit'];
    const filteredParams = this.removeParams(this.dataFilter?.customer_name?.value.split('&'), paramsToRemove);
    if (this.dataFilter?.customer_name?.value) {
      this.subscriptionManager.add(
        this.utilizationService.getClientIds(filteredParams).subscribe((res) => {
          if (res?.customer_ids) {
            this.dataFilter.customer_ids = res.customer_ids.join(',');
          } else {
            this.dataFilter.customer_ids = '';
          }
          this.cdf.detectChanges();
        })
      );
    } else {
      this.dataFilter.customer_name = null;
      this.dataFilter.customer_ids = '';
    }
  }

  getProjectsIds() {
    const paramsToRemove = ['offset', 'limit'];
    const filteredParams = this.removeParams(this.dataFilter?.project_name?.value.split('&'), paramsToRemove);
    if (this.dataFilter?.project_name?.value) {
      this.subscriptionManager.add(
        this.utilizationService.getProjectIds(filteredParams).subscribe((res) => {
          if (res?.project_ids) {
            this.dataFilter.project_ids = res.project_ids.join(',');
          } else {
            this.dataFilter.project_ids = '';
          }
          this.cdf.detectChanges();
        })
      );
    } else {
      this.dataFilter.project_name = null;
      this.dataFilter.project_ids = '';
    }
  }

  removeParams(params, paramsToRemove) {
    return params
      .filter((param) => {
        const [key, value] = param.split('=');
        return !paramsToRemove.includes(key) && value !== '' && value !== null;
      })
      .join('&');
  }

  async getProjections() {
    let queryFilter: QueryFilterParams = {};
    queryFilter.projection_detail_level = 'position_monthly';
    if (!this.dataFilter.start_date && !this.dataFilter.end_date) {
      const date = new Date();
      queryFilter.start_date = this.datePipe.transform(new Date(date.getFullYear(), date.getMonth(), 1), AppConstants.format);
      queryFilter.end_date = this.datePipe.transform(new Date(date.getFullYear(), date.getMonth() + 3, 0), AppConstants.format);
    }
    if (this.dataFilter) {
      this.cacheFilter.setCacheFilters({ ...this.dataFilter, ...queryFilter }, 'PL');
      for (const [key] of Object.entries(this.dataFilter)) {
        if (this.dataFilter[key] && (key === 'start_date' || key === 'end_date' || key === 'effective_date')) {
          if (key === 'effective_date') {
            queryFilter[`${key}`] = moment(new Date(this.dataFilter[key])).format('YYYY-MM-DDTHH:mm:ss.SSSSSS') + 'Z';
          } else {
            queryFilter[`${key}`] = this.datePipe.transform(this.dataFilter[key], AppConstants.format);
          }
        } else {
          queryFilter[`${key}`] = this.dataFilter[key];
        }
      }
      if (this.dataFilter?.client?.value && this.dataFilter?.customer_ids) {
        queryFilter['customer_ids'] = this.dataFilter.customer_ids + ',' + this.dataFilter.client.value;
        delete queryFilter['client'];
      } else if (this.dataFilter?.client?.value) {
        queryFilter['customer_ids'] = Number(this.dataFilter.client.value);
        delete queryFilter['client'];
      }
      delete queryFilter['dataType'];
      delete queryFilter['customer_name'];
      delete queryFilter['project_name'];
      delete queryFilter['date'];
      delete queryFilter['year'];
      delete queryFilter['period'];
      delete queryFilter['quarter'];
      delete queryFilter['status'];
      delete queryFilter['start_month'];
      delete queryFilter['end_month'];
      delete queryFilter['selectedProject'];
      delete queryFilter['selectedClient'];
      delete queryFilter['projectName'];
    }

    queryFilter = this.queryStringUtil(queryFilter);
    this.loading$.next(false);
    this.loading$.next(true);
    this.finalProjectionData = [];
    let flag = 0;

    // Making param consistent for API call that has been stored in BE cache
    const entries = Object.entries(queryFilter);
    let consistentParam = {};
    const numericKeys = ['project_ids', 'customer_ids'];
    const splitAndSortKeys = ['statuses', 'tags'];
    entries.sort((a, b) => a[0].localeCompare(b[0]));

    for (const [key, value] of entries) {
      if (numericKeys.includes(key)) {
        const numericValues = value.split(',').map(Number);
        numericValues.sort((a, b) => a - b);
        consistentParam[key] = numericValues.join(',');
      } else if (splitAndSortKeys.includes(key)) {
        const nonNumericValues = value.split(',').sort();
        consistentParam[key] = nonNumericValues.join(',');
      } else {
        consistentParam[key] = value;
      }
    }

    const shouldContinue = await this.getPausedProjectList(consistentParam['project_ids']);
    if (!shouldContinue) {
      return;
    }
    this.loading$.next(true);

    this.subscriptionManager.add(
      this.projectService
        .getProjectsProfitLoss(consistentParam)
        .pipe(
          map((res) => {
            if (res.status === 202) {
              flag++;
              throw res;
            }
            return res;
          }),
          retryWhen((errors) =>
            errors.pipe(
              switchMap((val) => {
                if (flag >= 15) {
                  return of(val).pipe(delay(15000), take(2));
                } else if (flag < 15) {
                  return of(val).pipe(delay(1000), take(15));
                } else {
                  return throwError(errors);
                }
              })
            )
          ),
          catchError((err) => throwError(err))
        )
        .subscribe(
          async (res) => {
            if (res) {
              this.projections = res.body['data'];
              // await this.sortByProjectName();
              await this.sortByPositionName();
              this.makeRowsSameHeight();
              this.prepareTableHeaders();
            }
          },
          () => this.loading$.next(false)
        )
    );
  }

  makeRowsSameHeight() {
    setTimeout(() => {
      if (document.getElementsByClassName('p-treetable-scrollable-wrapper').length) {
        const wrapper = document.getElementsByClassName('p-treetable-scrollable-wrapper');
        for (var i = 0; i < wrapper.length; i++) {
          const w = wrapper.item(i) as HTMLElement;
          const frozen_rows: any = w.querySelectorAll('.p-treetable-frozen-view tr');
          const unfrozen_rows: any = w.querySelectorAll('.p-treetable-unfrozen-view tr');
          for (let i = 0; i < frozen_rows.length; i++) {
            if (frozen_rows[i].clientHeight > unfrozen_rows[i].clientHeight) {
              unfrozen_rows[i].style.height = frozen_rows[i].clientHeight + 'px';
            } else if (frozen_rows[i].clientHeight < unfrozen_rows[i].clientHeight) {
              frozen_rows[i].style.height = unfrozen_rows[i].clientHeight + 'px';
            }
          }
        }
        this.layoutConfigService.updateHeight$.next(true);
        this.height = 'calc((var(--fixed-content-height, 1vh) * 100) - 160px)';
      }
    });
  }

  async sortByProjectName(): Promise<void> {
    return new Promise((resolve) => {
      if (this.projections?.projects?.length) {
        this.projections.projects.sort((a, b) => {
          const fa = a?.project?.name?.toLowerCase();
          const fb = b?.project?.name?.toLowerCase();
          if (fa < fb) {
            return -1;
          }
          if (fa > fb) {
            return 1;
          }
          return 0;
        });
      }
      resolve();
    });
  }

  async sortByPositionName(): Promise<void> {
    return new Promise((resolve) => {
      this.projections?.projects?.forEach((projects) => {
        const valid_monthly_projections = projects?.project?.projection?.valid_monthly_projections;
        valid_monthly_projections?.forEach((projection) => {
          if (projection) {
            const valid_positions = projection?.valid_monthly_projection?.validated_monthly_positions;
            const positionWithoutProjectExpense = valid_positions?.filter((position) => position.validated_monthly_position?.position?.name !== 'project_expenses');
            const positionWithProjectExpense = valid_positions?.filter((position) => position.validated_monthly_position?.position?.name === 'project_expenses');
            positionWithoutProjectExpense?.sort((a, b) => {
              const fa = a?.validated_monthly_position?.position?.name?.toLowerCase();
              const fb = b?.validated_monthly_position?.position?.name?.toLowerCase();

              if (fa < fb) {
                return -1;
              }
              if (fa > fb) {
                return 1;
              }
              return 0;
            });
            positionWithoutProjectExpense?.push(positionWithProjectExpense[0]);
            projection.valid_monthly_projection.validated_monthly_positions = positionWithoutProjectExpense;
          }
        });
      });
      resolve();
    });
  }

  ngOnDestroy() {
    this.ktDialogService.hide();
  }

  prepareTableHeaders() {
    this.tableHeaders = [];
    this.projectsRevenue = [];
    this.projectsExpense = [];
    this.bench = [];
    this.workExceptions = [];
    this.revenueTreeData = {
      data: { type: FINANCIAL_REVIEW_TYPES.REVENUE },
      children: []
    };
    this.expenseTreeData = {
      data: { type: FINANCIAL_REVIEW_TYPES.EXPENSE },
      children: []
    };
    this.grossProfitTreeData = {
      data: { type: FINANCIAL_REVIEW_TYPES.GROSS_PROFIT },
      children: []
    };
    this.grossMarginTreeData = {
      data: { type: FINANCIAL_REVIEW_TYPES.GROSS_MARGIN },
      children: []
    };
    this.benchTreeData = {
      data: { type: FINANCIAL_REVIEW_TYPES.BENCH },
      children: []
    };
    this.projectExpenseTreeData = {
      data: { type: FINANCIAL_REVIEW_TYPES.PROJECTS },
      children: []
    };
    this.WorkExpenseTreeData = {
      data: { type: FINANCIAL_REVIEW_TYPES.WORK_EXCEPTION },
      children: []
    };
    this.pLAdjustData = {
      data: { type: FINANCIAL_REVIEW_TYPES.PL_ADJUST }
    };
    this.cOGSAdjust = {
      data: { type: FINANCIAL_REVIEW_TYPES.COGS_ADJUST }
    };
    this.sgaAdjustData = {
      data: { type: FINANCIAL_REVIEW_TYPES.SGA_ADJUST }
    };
    this.netProfit = {
      data: { type: FINANCIAL_REVIEW_TYPES.NET_PROFIT }
    };
    this.netMargin = {
      data: { type: FINANCIAL_REVIEW_TYPES.NET_MARGIN }
    };
    this.fixedRevenueTreeData = [];
    this.tAndMRevenueTreeData = [];
    this.peopleExpenseTreeData = [];
    this.grossMarginData = [];
    this.grossProfitData = [];

    for (let i = 0; i < this.projections?.projects?.length; i++) {
      this.fixedRevenueTreeData[i] = {
        data: { type: FINANCIAL_REVIEW_TYPES.FIXED_REVENUE }
      };
      this.projectsRevenue[i] = {
        data: { type: this.projections.projects[i].project?.customer?.name, name: this.projections.projects[i].project.name },
        children: []
      };
      this.projectsExpense[i] = {
        data: { type: this.projections.projects[i].project?.customer?.name, name: this.projections.projects[i].project.name },
        children: []
      };
      this.tAndMRevenueTreeData[i] = {
        data: { type: FINANCIAL_REVIEW_TYPES.T_M_REVENUE },
        children: []
      };
      this.peopleExpenseTreeData[i] = {
        data: { type: FINANCIAL_REVIEW_TYPES.PEOPLE_EXPENSE },
        children: []
      };
      this.grossProfitData[i] = {
        data: { type: this.projections.projects[i].project?.customer?.name, name: this.projections.projects[i].project.name }
      };

      this.grossMarginData[i] = {
        data: { type: this.projections.projects[i].project?.customer?.name, name: this.projections.projects[i].project.name }
      };

      for (const validMonthlyProjection of this.projections?.projects[i]?.project?.projection?.valid_monthly_projections) {
        const monthlyProjection = validMonthlyProjection?.valid_monthly_projection;
        if (monthlyProjection) {
          const month = monthlyProjection.month.toString().length === 2 ? monthlyProjection.month : '0' + monthlyProjection.month;
          const tableHeader: TableHeader = {
            month: monthlyProjection.month,
            monthLabel: `${MONTH_NAMES[monthlyProjection.month - 1]} ${monthlyProjection.year}`,
            year: monthlyProjection.year,
            id: Number(`${monthlyProjection.year}${month}`)
          };

          //add header only if month not already exist and maintain sort order too
          if (!this.tableHeaders.find((header) => header.id === tableHeader.id)) {
            this.tableHeaders = [...this.tableHeaders, tableHeader];
          }

          this.prepareTreeRowData(i, tableHeader, monthlyProjection);
        }
      }
      for (let i = 0; i < this.projections?.utilizations?.length; i++) {
        this.bench[i] = {
          data: {
            type: `${this.projections?.utilizations[i]?.employee?.first_name} ${this.projections?.utilizations[i]?.employee?.last_name || ''}`,
            id: `${this.projections?.utilizations[i]?.employee?.id}`
          },
          children: []
        };
        for (const utilization of this.projections?.utilizations[i]?.employee?.utilizations) {
          const monthlyUtilization = utilization?.utilization;
          if (monthlyUtilization) {
            const month = monthlyUtilization.month.toString().length === 2 ? monthlyUtilization.month : '0' + monthlyUtilization.month;
            const tableHeader: TableHeader = {
              month: monthlyUtilization.month,
              monthLabel: `${MONTH_NAMES[monthlyUtilization.month - 1]} ${monthlyUtilization.year}`,
              year: monthlyUtilization.year,
              id: Number(`${monthlyUtilization.year}${month}`)
            };

            this.prepareBenchTreeRowData(i, tableHeader, monthlyUtilization);
          }
        }
      }
      for (let i = 0; i < this.projections?.employees_plus_work_exceptions?.length; i++) {
        this.workExceptions[i] = {
          data: {
            type: `${this.projections?.employees_plus_work_exceptions[i]?.employee?.first_name} ${this.projections?.employees_plus_work_exceptions[i]?.employee?.last_name || ''}`,
            id: `${this.projections?.employees_plus_work_exceptions[i]?.employee?.id}`
          },
          children: []
        };
        for (const workExceptions of this.projections?.employees_plus_work_exceptions[i]?.employee?.work_exception_summary) {
          const monthlyUtilization = workExceptions?.work_exceptions[0]?.work_exception;
          if (monthlyUtilization) {
            const year = monthlyUtilization.date.split('-')[0];
            const month = monthlyUtilization.date.split('-')[1];
            const tableHeader: TableHeader = {
              month: month,
              monthLabel: `${MONTH_NAMES[+month - 1]} ${year}`,
              year: +year,
              id: Number(`${year}${month}`)
            };
            this.prepareWorkExceptionsTreeRowData(i, tableHeader, workExceptions);
          }
        }
      }
      for (const totalProjection of this.projections.totals_monthly) {
        const monthlyTotal = totalProjection.monthly_total;
        const month = monthlyTotal.month.toString().length === 2 ? monthlyTotal.month : '0' + monthlyTotal.month;
        const tableHeader = {
          id: Number(`${monthlyTotal.year}${month}`)
        };
        this.prepareTotals(tableHeader, monthlyTotal, this.revenueTreeData, 'revenue');
        this.prepareTotals(tableHeader, monthlyTotal, this.pLAdjustData, 'revenue_plugs');
        this.prepareTotals(tableHeader, monthlyTotal, this.sgaAdjustData, 'sga_plugs');
        this.prepareTotals(tableHeader, monthlyTotal, this.netMargin, 'percent_net_profit');
        this.prepareTotals(tableHeader, monthlyTotal, this.netProfit, 'net_profit');
        this.prepareTotals(tableHeader, monthlyTotal, this.expenseTreeData, this.costConstant);
        this.prepareTotals(tableHeader, monthlyTotal, this.cOGSAdjust, 'expense_plugs');
        this.prepareTotals(tableHeader, monthlyTotal, this.grossProfitTreeData, 'grossProfit');
        this.prepareTotals(tableHeader, monthlyTotal, this.grossMarginTreeData, 'grossMargin');
        this.prepareTotals(tableHeader, monthlyTotal, this.benchTreeData, 'unutilized_cost');
        this.prepareTotals(tableHeader, monthlyTotal, this.projectExpenseTreeData, 'cost_less_unutilized');
        this.prepareTotals(tableHeader, monthlyTotal, this.WorkExpenseTreeData, 'work_exception_cost');
      }
    }

    this.prepareFinalProjectionData();
    this.loading$.next(false);
    this.cdf.detectChanges();
  }

  prepareTotals(tableHeader, totalProjection, parentTreeNode: TreeNode, valueFieldName: string) {
    if (valueFieldName === 'grossProfit') {
      parentTreeNode.data[tableHeader.id] = Number(totalProjection['revenue']) - Number(totalProjection[this.costConstant]);
    }
    if (valueFieldName === 'grossMargin') {
      if (Number(totalProjection['revenue'])) {
        parentTreeNode.data[tableHeader.id] = ((Number(totalProjection['revenue']) - Number(totalProjection[this.costConstant])) / Number(totalProjection['revenue'])) * 100;
      } else {
        parentTreeNode.data[tableHeader.id] = 0;
      }
    }
    if (valueFieldName === 'percent_net_profit') {
      parentTreeNode.data[tableHeader.id] = Number(totalProjection[valueFieldName]) * 100;
    }
    if (
      valueFieldName === 'revenue' ||
      valueFieldName === 'sga_plugs' ||
      valueFieldName === 'net_profit' ||
      valueFieldName === 'revenue_plugs' ||
      valueFieldName === this.costConstant ||
      valueFieldName === 'expense_plugs' ||
      valueFieldName === 'unutilized_cost' ||
      valueFieldName === 'cost_less_unutilized' ||
      valueFieldName === 'work_exception_cost'
    ) {
      parentTreeNode.data[tableHeader.id] = Number(totalProjection[valueFieldName]);
    }
  }

  private prepareTreeRowData(projectIndex, tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection) {
    this.prepareRevenueTreeRowData(projectIndex, tableHeader, validMonthlyProjection);
    this.prepareExpenseTreeRowData(projectIndex, tableHeader, validMonthlyProjection);
    this.prepareGrossProfitRowData(projectIndex, tableHeader, validMonthlyProjection);
    this.prepareGrossMarginRowData(projectIndex, tableHeader, validMonthlyProjection);
  }

  private prepareRevenueTreeRowData(projectIndex, tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection) {
    this.prepareTandMRevenueTreeRowData(projectIndex, tableHeader, validMonthlyProjection);
    this.prepareFixedRevenueTreeRowData(projectIndex, tableHeader, validMonthlyProjection);
  }

  private prepareGrossProfitRowData(projectIndex, tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection) {
    this.prepareNodeData(projectIndex, tableHeader, validMonthlyProjection, this.grossProfitData[projectIndex], 'grossProfit');
  }

  private prepareGrossMarginRowData(projectIndex, tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection) {
    this.prepareNodeData(projectIndex, tableHeader, validMonthlyProjection, this.grossMarginData[projectIndex], 'grossMargin');
  }

  private prepareFixedRevenueTreeRowData(projectIndex, tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection) {
    this.prepareNodeData(projectIndex, tableHeader, validMonthlyProjection, this.fixedRevenueTreeData[projectIndex], 'revenue');
  }

  private prepareTandMRevenueTreeRowData(projectIndex, tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection) {
    this.prepareChildNodeData(projectIndex, tableHeader, validMonthlyProjection, this.projectsRevenue[projectIndex], 'revenue');
  }

  private prepareExpenseTreeRowData(projectIndex, tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection) {
    this.preparePeopleExpenseTreeRowData(projectIndex, tableHeader, validMonthlyProjection);
  }

  private preparePeopleExpenseTreeRowData(projectIndex, tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection) {
    this.prepareChildNodeData(projectIndex, tableHeader, validMonthlyProjection, this.projectsExpense[projectIndex], 'cost');
  }

  private prepareBenchTreeRowData(employeeIndex, tableHeader: TableHeader, monthlyUtilization) {
    this.bench[employeeIndex].data[tableHeader.id] = Number(monthlyUtilization['amount']);
  }

  private prepareWorkExceptionsTreeRowData(employeeIndex, tableHeader: TableHeader, workExceptions) {
    this.workExceptions[employeeIndex].data[tableHeader.id] = Number(workExceptions.work_exception_cost);
  }

  private prepareNodeData(projectIndex, tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection, parentTreeNode: TreeNode, valueFieldName: string) {
    parentTreeNode.data[tableHeader.id] = Number(validMonthlyProjection[valueFieldName]);
    if (valueFieldName === 'grossProfit') {
      parentTreeNode.data[tableHeader.id] = Number(validMonthlyProjection['revenue']) - Number(validMonthlyProjection[this.costConstant]);
    }
    if (valueFieldName === 'grossMargin') {
      parentTreeNode.data[tableHeader.id] = Number(validMonthlyProjection['percent_gross_margin']) * 100;
    }
  }

  private prepareChildNodeData(projectIndex, tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection, parentTreeNode: TreeNode, valueFieldName: string) {
    let projectExpenseRevenue = false;
    parentTreeNode.data[tableHeader.id] = Number(validMonthlyProjection[valueFieldName]);

    for (const [index, validMonthlyPosition] of validMonthlyProjection.validated_monthly_positions.entries()) {
      const validatedMonthlyPosition = validMonthlyPosition?.validated_monthly_position;
      if (validatedMonthlyPosition) {
        let peopleExpensePosition = {};
        const existingRevenuePositionIndex = parentTreeNode.children.findIndex((child) => child?.data?.id === validatedMonthlyPosition?.position.id);

        if (existingRevenuePositionIndex > -1) {
          peopleExpensePosition = parentTreeNode.children[existingRevenuePositionIndex];
          peopleExpensePosition['data'][tableHeader.id] = Number(validatedMonthlyPosition[valueFieldName]);
          parentTreeNode.children[existingRevenuePositionIndex] = peopleExpensePosition;
        } else {
          if (valueFieldName === 'revenue' && validatedMonthlyPosition?.position?.name === 'project_expenses') {
            // projectExpenseRevenue = true; // commented this as we need to show position which got removed as well under the revenue
          }
          if (!projectExpenseRevenue) {
            peopleExpensePosition = {
              data: {
                id: validatedMonthlyPosition?.position?.id,
                type: this.getTheTypeName(validatedMonthlyPosition?.position?.name),
                // type: validatedMonthlyPosition?.position?.name,
                name: `${validatedMonthlyPosition?.position?.employee?.first_name || '---'} ${validatedMonthlyPosition?.position?.employee?.last_name || '---'}`
              }
            };
            peopleExpensePosition['data'][tableHeader.id] = Number(validatedMonthlyPosition[valueFieldName]);
            parentTreeNode.children = [...parentTreeNode.children, peopleExpensePosition];
            if (valueFieldName === this.costConstant) {
              const targetIndex = parentTreeNode.children.findIndex((obj) => obj.data.type === 'Project Expenses');

              if (targetIndex !== -1) {
                const targetObject = parentTreeNode.children.splice(targetIndex, 1)[0];
                parentTreeNode.children.push(targetObject);
              }
            }
          }
        }
      }
    }
  }

  private getTheTypeName(positionNameDiff: string): string {
    if (positionNameDiff?.includes('_')) {
      positionNameDiff = positionNameDiff.replace('_', ' ');
      positionNameDiff = positionNameDiff.replace(/\w\S*/g, (txt) => {
        return txt.charAt(0).toUpperCase() + txt.slice(1).toLowerCase();
      });
    }
    return positionNameDiff;
  }

  private addZeroToNonAvailableData() {
    for (const treeNodeRowData of this.finalProjectionData) {
      this.addNotFoundHeaderKeyValue(treeNodeRowData.data);
      const rowDataChildren = treeNodeRowData.children;
      this.addZeroToTreeChild(rowDataChildren);
    }
  }

  private addZeroToTreeChild(children) {
    while (children && children.length) {
      for (const rowDataChild of children) {
        this.addNotFoundHeaderKeyValue(rowDataChild.data);
        children = rowDataChild.children;
        if (children) {
          this.addZeroToTreeChild(children);
        }
      }
    }
  }

  private addNotFoundHeaderKeyValue(data) {
    if (data) {
      let rowDataKeys: any[] = Object.keys(data);
      rowDataKeys = rowDataKeys.map((key) => Number(key));
      const notFoundHeaderKeys = this.tableHeaders.filter((tableHeader) => !rowDataKeys.includes(tableHeader.id)).map((tableHeader) => tableHeader.id);
      if (notFoundHeaderKeys && notFoundHeaderKeys.length) {
        for (const notFoundHeaderKey of notFoundHeaderKeys) {
          data[notFoundHeaderKey] = 0;
        }
      }
    }
  }

  prepareFinalProjectionData() {
    this.finalProjectionData = [];
    this.finalProjectionData = [
      this.getRevenueTree(),
      this.getExpenseTree(),
      this.getGrossProfitData(),
      this.getGrossMarginData(),
      this.sgaAdjustData,
      this.netProfit,
      this.netMargin
    ];
    this.layoutConfigService.updateHeight$.next(true);
    this.height = 'calc((var(--fixed-content-height, 1vh) * 100) - 160px)';
    this.addZeroToNonAvailableData();
    this.makeRowsSameHeight();
  }

  getGrossProfitData() {
    this.grossProfitTreeData = {
      ...this.grossProfitTreeData,
      children: this.grossProfitData
    };
    return this.grossProfitTreeData;
  }

  getGrossMarginData() {
    this.grossMarginTreeData = {
      ...this.grossMarginTreeData,
      children: this.grossMarginData
    };
    return this.grossMarginTreeData;
  }

  getExpenseTree(): TreeNode {
    this.expenseTreeData = {
      ...this.expenseTreeData,
      children: [this.getBenchTree(), this.getProjectExpenseTree(), this.getWorkExceptionTree(), this.cOGSAdjust]
    };
    return this.expenseTreeData;
  }

  getBenchTree(): TreeNode {
    this.benchTreeData = {
      ...this.benchTreeData,
      children: this.getBench()
    };
    return this.benchTreeData;
  }

  getProjectExpenseTree(): TreeNode {
    this.projectExpenseTreeData = {
      ...this.projectExpenseTreeData,
      children: this.getProjectExpense()
    };
    return this.projectExpenseTreeData;
  }

  getWorkExceptionTree(): TreeNode {
    this.WorkExpenseTreeData = { ...this.WorkExpenseTreeData, children: this.getWorkException() };
    return this.WorkExpenseTreeData;
  }

  getRevenueTree(): TreeNode {
    this.revenueTreeData = {
      ...this.revenueTreeData,
      children: [...this.getProjectRevenue(), this.getPLAdjustData()]
    };
    return this.revenueTreeData;
  }

  getPLAdjustData() {
    return this.pLAdjustData;
  }

  getProjectRevenue() {
    for (const projectrevenue of this.projectsRevenue) {
      projectrevenue.children.forEach((child) => {
        delete child.data.id;
      });
    }
    return this.projectsRevenue;
  }

  getProjectExpense() {
    for (const projectexpense of this.projectsExpense) {
      projectexpense.children.forEach((child) => {
        delete child.data.id;
      });
    }
    return this.projectsExpense;
  }

  getBench() {
    for (const bench of this.bench) {
      bench.children.forEach((child) => {
        delete child.data.id;
      });
    }
    return this.bench;
  }

  getWorkException() {
    for (const bench of this.workExceptions) {
      bench.children.forEach((child) => {
        delete child.data.id;
      });
    }
    return this.workExceptions;
  }

  getIsProjectTandM(projectIndex): boolean {
    return this.projections?.projects[projectIndex]?.project.billing_type === BillingTypes.TIME_MATERIALS;
  }

  getStyle(rowNode) {
    if (rowNode?.level === 0) {
      return 'font-weight-bold';
    }
    if (rowNode?.level === 1) {
      return 'children-level-1';
    }
  }

  getMonthlyValues(rowData: TreeNode, rowNode) {
    if (rowData) {
      const montlyData = JSON.parse(JSON.stringify(rowData));
      const typeExcluded = this.omit('type', montlyData);
      const idExcluded = this.omit('id', typeExcluded);
      return this.omit('name', idExcluded);
    }
    return;
  }

  private omit(key, obj) {
    const { [key]: omitted, ...rest } = obj;
    return rest;
  }

  preserveOriginalOrder = (a: KeyValue<number, string>, b: KeyValue<number, string>): number => {
    return 0;
  };

  getTotal(rowData: TreeNode, rowNode?) {
    if (this.projections?.projects) {
      if (rowData?.type) {
        const profit = this.projections?.total?.revenue - this.projections?.total?.cost;
        switch (rowData.type) {
          case FINANCIAL_REVIEW_TYPES.GROSS_MARGIN:
            if (this.projections?.total?.revenue === 0) {
              return 0;
            }
            return (profit / this.projections?.total?.revenue) * 100;
          case FINANCIAL_REVIEW_TYPES.REVENUE:
            return this.projections.total?.revenue;
          case FINANCIAL_REVIEW_TYPES.EXPENSE:
            return this.projections.total?.cost;
          case FINANCIAL_REVIEW_TYPES.GROSS_PROFIT:
            return profit;
          case FINANCIAL_REVIEW_TYPES.BENCH:
            return this.projections?.total?.unutilized_cost;
          case FINANCIAL_REVIEW_TYPES.PROJECTS:
            return this.projections?.total?.cost_less_unutilized;
          case FINANCIAL_REVIEW_TYPES.WORK_EXCEPTION:
            return this.projections?.total?.work_exception_cost;
          case FINANCIAL_REVIEW_TYPES.PL_ADJUST:
            return this.projections?.total?.revenue_plugs;
          case FINANCIAL_REVIEW_TYPES.COGS_ADJUST:
            return this.projections?.total?.expense_plugs;
          case FINANCIAL_REVIEW_TYPES.SGA_ADJUST:
            return this.projections?.total?.sga_plugs;
          case FINANCIAL_REVIEW_TYPES.NET_PROFIT:
            return this.projections?.total?.net_profit;
          case FINANCIAL_REVIEW_TYPES.NET_MARGIN:
            return this.projections?.total?.percent_net_profit * 100;
        }
      }
      let total;
      if (rowNode?.node?.parent?.data?.type === FINANCIAL_REVIEW_TYPES.GROSS_MARGIN) {
        // rowData.type is customer name
        for (let i = 0; i < this.projections?.projects?.length; i++) {
          if (this.projections.projects[i].project.customer.name === rowData.type && this.projections.projects[i].project.name === rowData['name']) {
            return this.projections.projects[i].project.projection.percent_gross_margin * 100;
          }
        }
      } else {
        const rowDataWithoutType = this.omit('type', rowData);
        const rowDataWithoutName = this.omit('name', rowDataWithoutType);
        const rowDataWithoutId = this.omit('id', rowDataWithoutName);
        total = this.sumValues(rowDataWithoutId);
        if (rowData?.type === FINANCIAL_REVIEW_TYPES.REVENUE) {
          this.revenueTotal = total;
        }
        if (rowData?.type === FINANCIAL_REVIEW_TYPES.GROSS_PROFIT) {
          this.grossProfitTotal = total;
        }
      }
      return total;
    } else {
      return 0;
    }
  }

  sumValues = (obj) =>
    Object.values(obj).reduce((a: any, b: any) => {
      return a + b;
    }, 0);

  openSideBar() {
    this.openFilter = true;
  }

  applyTags() {
    this.tags = [];
    if (!this.dataFilter) {
      return;
    }

    if (this.dataFilter.dataType === 'specific' && this.dataFilter.effective_date) {
      this.tags.push({
        label: 'Historical Data',
        value: this.datePipe.transform(this.dataFilter.effective_date, 'MM/dd/yyyy, hh:mm a'),
        key: ['dataType', 'effective_date']
      });
    }
    if (this.dataFilter.rollingOption) {
      this.tags.push({
        label: 'Rolling',
        value: this.dataFilter.rollingOption.toString(),
        key: ['start_date', 'end_date', 'rollingOption']
      });
    }
    if (this.dataFilter.quarter) {
      this.tags.push({
        label: 'By Quarter',
        value: this.dataFilter.quarter,
        key: ['period', 'quarter', 'start_date', 'end_date']
      });
    }
    if (this.dataFilter.year) {
      this.tags.push({
        label: 'By Year',
        value: this.dataFilter.year.toString(),
        key: ['period', 'year', 'start_date', 'end_date']
      });
    }
    if (this.dataFilter.start_date && this.dataFilter.end_date && !this.dataFilter.rollingOption && !this.dataFilter.year && !this.dataFilter.quarter) {
      const dateFormat = 'MM/dd/yyyy';
      const value = this.datePipe.transform(this.dataFilter.start_date, dateFormat) + ' - ' + this.datePipe.transform(this.dataFilter.end_date, dateFormat);
      this.tags.push({
        label: 'Date Range',
        value: value,
        key: ['period', 'start_date', 'end_date', 'start_month', 'end_month']
      });
    }

    if (this.dataFilter.project_name) {
      this.tags.push({
        label: 'Project Group',
        value: this.dataFilter.project_name.name,
        key: ['project_name', 'project_ids']
      });
    }
    if (this.dataFilter.include_utilizations !== undefined) {
      this.tags.push({
        label: 'Include Bench',
        value: this.dataFilter.include_utilizations ? 'True' : 'False',
        key: ['include_utilizations']
      });
    }
    if (this.dataFilter.include_work_exceptions !== undefined) {
      this.tags.push({
        label: 'Include Work Exception',
        value: this.dataFilter.include_work_exceptions ? 'True' : 'False',
        key: ['include_work_exceptions']
      });
    }
    if (this.dataFilter.include_pl_plugs !== undefined) {
      this.tags.push({
        label: 'Include P&L Plugs',
        value: this.dataFilter.include_pl_plugs ? 'True' : 'False',
        key: ['include_pl_plugs']
      });
    }

    if (this.dataFilter.customer_name) {
      this.tags.push({
        label: 'Client Group',
        value: this.dataFilter.customer_name.name,
        key: ['customer_name', 'customer_ids']
      });
    }
    if (this.dataFilter.client) {
      this.tags.push({
        label: 'Client',
        value: this.dataFilter.client.name,
        key: ['client']
      });
    }

    if (this.dataFilter.statuses) {
      this.tags.push({
        label: 'Project Status',
        value: this.dataFilter.statuses
      });
    }
    if (this.dataFilter?.ClientName?.length) {
      this.tags.push({
        label: 'Client',
        value: this.dataFilter?.ClientName?.join(','),
        key: ['clientName', 'customer_ids']
      });
    }
    if (this.dataFilter?.projectName?.length) {
      this.tags.push({
        label: 'Project',
        value: this.dataFilter?.projectName?.join(','),
        key: ['projectNameList', 'project_ids']
      });
    }
    if (this.dataFilter.tags) {
      const tagsToShow = this.dataFilter.tags
        .split(',')
        .map((tag) => tag.split(':').pop().split('__').pop())
        .filter((tag) => tag !== '');

      this.tags.push({
        label: 'Tags',
        value: tagsToShow,
        key: ['tags'],
        isResetButtonShown: true
      });
    }
  }

  getGrossMarginTotal(data) {
    const project = this.projections?.projects?.filter((projects) => projects?.project?.name === data.name)[0];
    return Math.round(Number(project?.project?.projection?.percent_gross_margin) * 100);
  }

  exportReport(type) {
    const fileName = `P&L_Report_${moment().format('YYYY')}_${moment().format('MM')}_${moment().format('DD')}_${moment().format('HH')}${moment().format('mm')}`;
    const colNames = {
      project: '1~project',
      type: '2~type',
      subType: '3~subType',
      name: '4~name',
      monthPrefix: 'm~',
      total: 'z~total'
    };
    const exportReportData = [];
    const originalExportReportData = [];

    if (type === 'excel') {
      const excelHeader = {
        [colNames.project]: 'P&L Category',
        [colNames.type]: 'Project',
        [colNames.subType]: 'Position',
        [colNames.name]: 'Name'
      };
      this.tableHeaders.forEach((header) => {
        excelHeader[colNames.monthPrefix + header.id.toString()] = header.monthLabel;
      });
      excelHeader[colNames.total] = 'Total';
      let parentNodeData;
      let subParentNodeData;
      const doAddChildData = (data = [], level = 1, parent = '', parentData = {}) => {
        let parentNode = parent;
        for (let i = 0; i < data.length; i++) {
          const rowData = {
            ...data[i].data,
            [colNames.project]: parentNodeData,
            [colNames.type]: data[i].data.type || '',
            [colNames.subType]: '',
            [colNames.name]: data[i].data.name || ''
          };

          if (level === 3 || level === 4) {
            rowData[colNames.subType] = rowData[colNames.type];
            rowData[colNames.type] = subParentNodeData;
          }
          if (parentNode === AppConstants.grossMargin) {
            rowData[colNames.total] = this.getGrossMarginTotal(data[i].data);
          } else {
            rowData[colNames.total] = this.getTotal(data[i].data);
          }
          if (data[i].data.type === FINANCIAL_REVIEW_TYPES.SGA_ADJUST) {
            rowData[colNames.total] = this.getTotal(data[i].data);
          }

          if (level === 2 && (parentNodeData === FINANCIAL_REVIEW_TYPES.GROSS_MARGIN || parentNodeData === FINANCIAL_REVIEW_TYPES.GROSS_PROFIT)) {
            rowData[colNames.project] = parentNodeData;
          }

          if (level === 1) {
            //project level
            rowData[colNames.project] = rowData[colNames.type];
            rowData[colNames.type] = '';
            parentNode = rowData[colNames.project];
          }

          //change object property names to maintain order to prevent column order issue in excel and csv

          if (type === 'excel') {
            Object.keys(rowData).forEach((prop) => {
              //month column will be in number and we need to prefix it
              if (!isNaN(Number(prop))) {
                rowData[colNames.monthPrefix + prop] = Math.round(rowData[prop]);
                delete rowData[prop];
              }
            });
            //removing not required fileds
            delete rowData.type;
            delete rowData.name;
            delete rowData.id;
            // delete rowData.subType
          }
          if (
            level === 1 &&
            (parentNode === FINANCIAL_REVIEW_TYPES.NET_MARGIN || parentNode === FINANCIAL_REVIEW_TYPES.NET_PROFIT || parentNode === FINANCIAL_REVIEW_TYPES.SGA_ADJUST)
          ) {
            exportReportData.push(rowData);
          } else if (level == 3 || level === 4) {
            exportReportData.push(rowData);
          } else if (
            level === 2 &&
            (data[i].data.type === FINANCIAL_REVIEW_TYPES.PL_ADJUST ||
              data[i].data.type === FINANCIAL_REVIEW_TYPES.COGS_ADJUST ||
              parentNodeData === FINANCIAL_REVIEW_TYPES.GROSS_PROFIT ||
              parentNodeData === FINANCIAL_REVIEW_TYPES.GROSS_MARGIN)
          ) {
            exportReportData.push(rowData);
          }

          if (data[i].children?.length > 0) {
            if (level === 1) {
              parentNodeData = data[i].data.type;
            }
            if (level === 2) {
              subParentNodeData = data[i].data.type;
            }
            doAddChildData(data[i].children, level + 1, data[i].data.type);
          }
        }
      };
      doAddChildData(this.finalProjectionData);
      const doOrigianlAddChildData = (data = [], level = 1, parent = '') => {
        let parentNode = parent;
        for (let i = 0; i < data.length; i++) {
          const rowData = {
            ...data[i].data,
            [colNames.project]: '',
            [colNames.type]: data[i].data.type || '',
            [colNames.subType]: '',
            [colNames.name]: data[i].data.name || ''
          };
          if (level === 3 || level === 4) {
            rowData[colNames.subType] = rowData[colNames.type];
            rowData[colNames.type] = '';
          }
          if (parentNode === AppConstants.grossMargin) {
            rowData[colNames.total] = this.getGrossMarginTotal(data[i].data);
          } else {
            rowData[colNames.total] = this.getTotal(data[i].data);
          }
          if (data[i].data.type === FINANCIAL_REVIEW_TYPES.SGA_ADJUST) {
            rowData[colNames.total] = this.getTotal(data[i].data);
          }

          if (level === 1) {
            //project level
            rowData[colNames.project] = rowData[colNames.type];
            rowData[colNames.type] = '';
            parentNode = rowData[colNames.project];

            //removing zeros from project level
            // Object.keys(rowData).forEach((prop) => {
            //   if (rowData[prop] === 0) {
            //     rowData[prop] = "";
            //   }
            // });
          }

          //change object property names to maintain order to prevent column order issue in excel and csv
          if (type === 'excel') {
            Object.keys(rowData).forEach((prop) => {
              const value = parseFloat(rowData[prop]);
              if (!isNaN(Number(prop))) {
                rowData[colNames.monthPrefix + prop] =
                  parentNode === AppConstants.grossMargin || parentNode === this.appConstants.netMargin
                    ? `${
                        value % 1 === 0
                          ? value.toFixed(0).replace(this.appConstants.numberWithCommasRegex, ',')
                          : value.toFixed(1).replace(this.appConstants.numberWithCommasRegex, ',')
                      }%`
                    : `$${value.toFixed(2).replace(this.appConstants.numberWithCommasRegex, ',')}`;
                delete rowData[prop];
              }
            });

            const totalValue = parseFloat(rowData[colNames.total]);
            rowData[colNames.total] =
              parentNode === AppConstants.grossMargin || parentNode === this.appConstants.netMargin
                ? `${
                    totalValue % 1 === 0
                      ? totalValue.toFixed(0).replace(this.appConstants.numberWithCommasRegex, ',')
                      : totalValue.toFixed(1).replace(this.appConstants.numberWithCommasRegex, ',')
                  }%`
                : `$${totalValue.toFixed(2).replace(this.appConstants.numberWithCommasRegex, ',')}`;

            //removing not required fileds
            delete rowData.type;
            delete rowData.name;
            delete rowData.id;
          }

          originalExportReportData.push(rowData);

          if (data[i].children?.length > 0) {
            doOrigianlAddChildData(data[i].children, level + 1, data[i].data.type);
          }
        }
      };
      doOrigianlAddChildData(this.finalProjectionData);

      if (type === 'excel') {
        this.utilizationService.exportFormattedExcel([excelHeader], exportReportData, fileName, originalExportReportData);
      }
    } else {
      if (type === 'csv') {
        Object.keys(colNames).forEach((col) => {
          colNames[col] = colNames[col].split('~')[1];
        });
      }

      const csvHeaders = ['P&L Category', 'Project', 'Position', 'Name', ...this.tableHeaders.map((header) => colNames.monthPrefix + header.monthLabel.toString()), 'Total'];

      const pdfHeader = [
        {
          title: 'P&L Category',
          dataKey: colNames.project
        },
        {
          title: 'Project',
          dataKey: colNames.type
        },
        {
          title: 'Position',
          dataKey: colNames.subType
        },
        {
          title: 'Name',
          dataKey: colNames.name
        },
        ...this.tableHeaders.map((header) => ({
          title: header.monthLabel,
          dataKey: colNames.monthPrefix + header.id.toString()
        })),
        {
          title: 'Total',
          dataKey: colNames.total
        }
      ];

      const doAddChildData = (data = [], level = 1, parent = '') => {
        let parentNode = parent;
        for (let i = 0; i < data.length; i++) {
          const rowData = {
            ...data[i].data,
            [colNames.project]: '',
            [colNames.type]: data[i].data.type || '',
            [colNames.subType]: '',
            [colNames.name]: data[i].data.name || ''
          };
          if (level === 3 || level === 4) {
            rowData[colNames.subType] = rowData[colNames.type];
            rowData[colNames.type] = '';
          }
          if (parentNode === AppConstants.grossMargin) {
            rowData[colNames.total] = this.getGrossMarginTotal(data[i].data);
          } else {
            rowData[colNames.total] = this.getTotal(data[i].data);
          }
          if (data[i].data.type === FINANCIAL_REVIEW_TYPES.SGA_ADJUST) {
            rowData[colNames.total] = this.getTotal(data[i].data);
          }

          if (level === 1) {
            //project level
            rowData[colNames.project] = rowData[colNames.type];
            rowData[colNames.type] = '';
            parentNode = rowData[colNames.project];

            //removing zeros from project level
            // Object.keys(rowData).forEach((prop) => {
            //   if (rowData[prop] === 0) {
            //     rowData[prop] = "";
            //   }
            // });
          }

          //change object property names to maintain order to prevent column order issue in excel and csv
          if (type === 'pdf' || type === 'csv') {
            Object.keys(rowData).forEach((prop) => {
              if (!isNaN(Number(prop))) {
                const value = parseFloat(rowData[prop]);

                rowData[colNames.monthPrefix + prop] =
                  parentNode === AppConstants.grossMargin || parentNode === this.appConstants.netMargin
                    ? `${
                        value % 1 === 0
                          ? value.toFixed(0).replace(this.appConstants.numberWithCommasRegex, ',')
                          : value.toFixed(1).replace(this.appConstants.numberWithCommasRegex, ',')
                      }%`
                    : `$${value.toFixed(2).replace(this.appConstants.numberWithCommasRegex, ',')}`;

                if (type === 'pdf') {
                  delete rowData[prop];
                }
              }
            });

            const totalValue = parseFloat(rowData[colNames.total]);
            rowData[colNames.total] =
              parentNode === this.appConstants.grossMargin || parentNode === this.appConstants.netMargin
                ? `${
                    totalValue % 1 === 0
                      ? totalValue.toFixed(0).replace(this.appConstants.numberWithCommasRegex, ',')
                      : totalValue.toFixed(1).replace(this.appConstants.numberWithCommasRegex, ',')
                  }%`
                : `$${totalValue.toFixed(2).replace(this.appConstants.numberWithCommasRegex, ',')}`;

            if (type === 'pdf') {
              delete rowData.type;
              delete rowData.name;
            }
          }

          exportReportData.push(rowData);

          if (data[i].children?.length > 0) {
            doAddChildData(data[i].children, level + 1, data[i].data.type);
          }
        }
      };
      doAddChildData(this.finalProjectionData);

      if (type === 'csv') {
        const updatedKeyMap = {
          [colNames.project]: 'P&L Category',
          [colNames.type]: 'Project',
          [colNames.subType]: 'Position',
          [colNames.name]: 'Name',
          [colNames.total]: 'Total'
        };

        const updatedKeyData = exportReportData.map((row) => {
          const updatedRow = {};

          Object.entries(row).forEach(([key, value]) => {
            const header = this.tableHeaders.find((h) => h.id.toString() === key);

            if (header) {
              updatedRow[header.monthLabel] = value;
            } else {
              updatedRow[updatedKeyMap[key] || key] = value;
            }
          });

          return updatedRow;
        });
        this.utilizationService.exportToCsv(updatedKeyData, fileName, csvHeaders);
      }
      if (type === 'pdf') {
        this.utilizationService.exportPdf(pdfHeader, exportReportData, fileName, 4);
      }
    }
  }

  convertToTitleCase(key: string): string {
    return key.charAt(0).toUpperCase() + key.slice(1).toLowerCase();
  }

  onCancelFilter(tagValue) {
    tagValue?.key?.forEach((key) => {
      if (key === 'include_utilizations' || key === 'include_work_exceptions' || key === 'include_pl_plugs') {
        this.dataFilter[key] = false;
      } else if (key === 'tags') {
        this.selectedTags = [];
        this.dataFilter[key] = null;
      } else this.dataFilter[key] = null;

      this.router.navigate([], { relativeTo: this.activatedRoute, queryParams: { filterId: this.selectedFilter?.query_filter?.id }, queryParamsHandling: 'merge' });
    });

    this.doFilterData();
  }

  onRemoveStatusFilter(statusValue) {
    if (statusValue.length) {
      this.dataFilter.statuses = statusValue.join(',');
      this.dataFilter.status = statusValue;
    } else {
      this.dataFilter.statuses = null;
      this.dataFilter.status = null;
    }
    this.doFilterData();
  }

  doFilterData() {
    this.applyTags();
    this.dateRangeCalculation();
    this.getProjections();
  }

  dateRangeCalculation() {
    if (this.dataFilter.rollingOption) {
      const { startDate, endDate } = this.getStartDateEndDateFromRolling(this.dataFilter.rollingOption);
      this.dataFilter.start_date = this.datePipe.transform(startDate, AppConstants.format);
      this.dataFilter.end_date = this.datePipe.transform(endDate, AppConstants.format);
      this.dataFilter.year = null;
      this.dataFilter.quarter = null;
      this.dataFilter.start_month = null;
      this.dataFilter.end_month = null;
    }

    if (this.dataFilter.year) {
      const startEndDate = this.getStartEndDateFromYear(this.dataFilter.year);
      this.dataFilter.start_date = this.datePipe.transform(startEndDate.start_date, AppConstants.format);
      this.dataFilter.end_date = this.datePipe.transform(startEndDate.end_date, AppConstants.format);
      this.dataFilter.quarter = null;
      this.dataFilter.rollingOption = null;
      this.dataFilter.start_month = null;
      this.dataFilter.end_month = null;
    }
    if (this.dataFilter.quarter) {
      const startEndDate = this.getStartEndDateFromQuarter(this.dataFilter.quarter);
      this.dataFilter.start_date = this.datePipe.transform(startEndDate.start_date, AppConstants.format);
      this.dataFilter.end_date = this.datePipe.transform(startEndDate.end_date, AppConstants.format);
      this.dataFilter.year = null;
      this.dataFilter.rollingOption = null;
      this.dataFilter.start_month = null;
      this.dataFilter.end_month = null;
    }
  }

  onCloseSideBar(isSubmit) {
    this.openFilter = false;
    if (isSubmit) {
      this.doFilterData();
    }
  }

  // used to take query string param and removes values which are not set in the query string
  queryStringUtil(queryStringParam: QueryFilterParams) {
    const queryFilter: QueryFilterParams = {};
    if (queryStringParam) {
      for (const [key] of Object.entries(queryStringParam)) {
        if (queryStringParam[key] === '' || queryStringParam[key] === null || queryStringParam[key] === undefined) {
          delete queryStringParam[key];
        }
      }
    }
    // inserting order by in the query param
    queryStringParam.order_by = 'asc:customer_name,asc:name';
    return queryStringParam;
  }

  showButton(rowNode) {
    if (rowNode?.level === 1 && rowNode?.node?.parent?.data?.type === AppConstants.grossMargin) {
      return false;
    }
    if (rowNode?.level === 1 && rowNode?.node?.parent?.data?.type === 'Gross Profit') {
      return false;
    }
    if (rowNode?.level !== 2) {
      return true;
    }
    return false;
  }
  resetFilter() {
    this.dataFilter = new IFilter();
    this.selectedTags = [];
    this.cacheFilter.resetCacheFilters('PL');
    this.tags = [];
    this.defaultFilters();
    this.dateRangeCalculation();
    this.getProjections();
    this.router.navigate([], { relativeTo: this.activatedRoute, queryParams: { filterId: this.selectedFilter?.query_filter?.id }, queryParamsHandling: 'merge' });
  }

  openSaveFilterList() {
    this.showFilterListDialog = true;
    this.showSavedFilter = true;
    this.cdf.detectChanges();
  }
  applyAllFilter() {
    if (this.dataFilter) {
      if (this.dataFilter.start_month) {
        this.dataFilter.start_month = moment(this.dataFilter.start_date).toDate();
      }
      if (this.dataFilter.end_month) {
        this.dataFilter.end_month = moment(this.dataFilter.end_date).toDate();
      }
      if (this.dataFilter?.statuses) {
        this.dataFilter.statuses = this.dataFilter.statuses.replace(/%2C/g, ',');
        this.dataFilter.status = this.dataFilter.statuses.split(',');
      }
      if (!this.dataFilter?.statuses) {
        this.dataFilter.statuses = '';
        this.dataFilter.status = [];
      }

      if (this.dataFilter?.project_grp_name && this.dataFilter?.project_grp_value) {
        this.dataFilter.project_name = { name: this.dataFilter.project_grp_name, value: this.dataFilter.project_grp_value.replace(/%3D/g, '=').replace(/%26/g, '&') };
        delete this.dataFilter.project_grp_name;
        delete this.dataFilter.project_grp_value;
      }
      if (this.dataFilter?.client_grp_name && this.dataFilter?.client_grp_value) {
        this.dataFilter.customer_name = { name: this.dataFilter.client_grp_name, value: this.dataFilter.client_grp_value.replace(/%3D/g, '=').replace(/%26/g, '&') };
        delete this.dataFilter.client_grp_name;
        delete this.dataFilter.client_grp_value;
      }
      if (this.dataFilter?.project_ids) {
        this.dataFilter.project_ids = this.dataFilter.project_ids.replace(/%2C/g, ',');
        this.dataFilter.showProjectFilter = true;
      }
      if (this.dataFilter?.position_ids) {
        this.dataFilter.position_ids = Number(this.dataFilter.position_ids);
      }
      if (this.dataFilter?.customer_ids) {
        this.dataFilter.customer_ids = this.dataFilter.customer_ids.replace(/%2C/g, ',');
        this.dataFilter.showClientFilter = true;
      }
      if (this.dataFilter?.ClientName?.length && this.dataFilter.customer_ids) {
        this.dataFilter.customer_ids = this.dataFilter.customer_ids.replace(/%2C/g, ',');
        this.dataFilter.clientName = this.dataFilter.customer_ids.split(',');
        this.dataFilter.ClientName = this.dataFilter.ClientName.toString().replace(/%2C/g, ',').split(',');
        this.dataFilter.showClientFilter = false;
      }

      if (this.dataFilter?.projectName?.length && this.dataFilter.project_ids) {
        this.dataFilter.project_ids = this.dataFilter.project_ids.replace(/%2C/g, ',');
        this.dataFilter.projectName = this.dataFilter.projectName.toString().replace(/%2C/g, ',').split(',');
        this.dataFilter.showProjectFilter = false;
      }

      if (this.dataFilter?.tags) {
        this.dataFilter.tags = this.dataFilter.tags.replace(/%3A/g, ':').replace(/%2C/g, ',');
      }

      if (this.dataFilter?.dataType === 'specific') {
        this.dataFilter.effective_date = new Date(this.dataFilter.effective_date.toString().replace(/%3A/g, ':'));
      }
    }
    this.applyTags();
  }

  applyFilter() {
    this.showSavedFilter = false;
    let dataFilter = JSON.parse('{"' + decodeURI(this.selectedFilter.query_filter.query_string).replace(/"/g, '\\"').replace(/&/g, '","').replace(/=/g, '":"') + '"}');
    if (dataFilter.include_utilizations) {
      dataFilter.include_utilizations = dataFilter.include_utilizations != 'false';
    }
    if (dataFilter.include_work_exceptions) {
      dataFilter.include_work_exceptions = dataFilter.include_work_exceptions != 'false';
    }
    if (dataFilter.include_pl_plugs) {
      dataFilter.include_pl_plugs = dataFilter.include_pl_plugs != 'false';
    }
    if (dataFilter.selectedClient) {
      const a = dataFilter?.selectedClient?.toString()?.replace(/%2C/g, ',')?.replace(/%3A/g, ':');
      dataFilter.selectedClient = JSON.parse(dataFilter?.selectedClient?.toString()?.replace(/%2C/g, ',')?.replace(/%3A/g, ':'));
    }
    if (dataFilter.selectedProject) {
      dataFilter.selectedProject = JSON.parse(dataFilter?.selectedProject?.toString().replace(/%2C/g, ',').replace(/%3A/g, ':'));
    }
    this.dataFilter = dataFilter;
    this.applyAllFilter();
    this.selectedFilter = null;
    this.filteredFilters.data.query_filters = this.availableFilters;
    this.getProjections();
    this.closeModal();
  }

  getStartDateEndDateFromRolling(rollingOption: string) {
    const currentDate = new Date();
    const rollingRelativeMonth = rollingOption.split(' ')[1];
    const rollingMonths = parseInt(rollingOption.split(' ')[2]);

    const startDate = new Date(currentDate);
    startDate.setDate(1);

    const endDate = new Date(startDate);
    endDate.setMonth(startDate.getMonth() + rollingMonths + 1, 0);

    if (rollingRelativeMonth === 'minus') {
      startDate.setMonth(currentDate.getMonth() - 1);
      endDate.setMonth(currentDate.getMonth() + rollingMonths, 0);
      endDate.setFullYear(currentDate.getFullYear());
    }

    return { startDate, endDate };
  }

  getStartEndDateFromQuarter(quarter) {
    const now = new Date();
    const quarterDates = {
      Q1: {
        start_date: new Date(now.getFullYear(), 0, 1, 0, 0, 0, 0),
        end_date: new Date(now.getFullYear(), 2, 31, 11, 59, 59, 999)
      },
      Q2: {
        start_date: new Date(now.getFullYear(), 3, 1, 0, 0, 0, 0),
        end_date: new Date(now.getFullYear(), 5, 30, 11, 59, 59, 999)
      },
      Q3: {
        start_date: new Date(now.getFullYear(), 6, 1, 0, 0, 0, 0),
        end_date: new Date(now.getFullYear(), 8, 30, 11, 59, 59, 999)
      },
      Q4: {
        start_date: new Date(now.getFullYear(), 9, 1, 0, 0, 0, 0),
        end_date: new Date(now.getFullYear(), 11, 31, 11, 59, 59, 999)
      }
    };
    return quarterDates[quarter];
  }
  getStartEndDateFromYear(year) {
    return {
      start_date: new Date(year, 0, 1, 0, 0, 0, 0),
      end_date: new Date(year, 11, 31, 11, 59, 59, 999)
    };
  }

  closeModal() {
    this.showFilterListDialog = false;
    this.selectedFilter = null;
    this.showEditDialog = false;
    this.editFilterObj = null;
    this.showNameError = false;
    this.showDeleteDialog = false;
    this.deleteFilterObj = null;
    this.showShareDialog = false;
    this.shareFilterObj = null;
    this.openFilter = false;
  }

  onSaveFilter() {
    let filter = JSON.parse(JSON.stringify(this.dataFilter));
    if (filter?.yearValue) {
      filter.start_date = new Date(new Date(filter?.start_date).getFullYear(), new Date(filter?.start_date).getMonth(), new Date(filter?.start_date).getDate() + 1)
        ?.toISOString()
        .split('T')[0];
      filter.end_date = new Date(new Date(filter.end_date).getFullYear(), new Date(filter.end_date).getMonth(), new Date(filter.end_date).getDate() + 1)
        ?.toISOString()
        .split('T')[0];
    }
    filter = this.queryStringUtil(filter);
    if (this.dataFilter?.project_name?.name) {
      filter.project_grp_name = this.dataFilter?.project_name?.name;
      filter.project_grp_value = this.dataFilter?.project_name?.value;
    }
    if (this.dataFilter?.customer_name?.name) {
      filter.client_grp_name = this.dataFilter?.customer_name?.name;
      filter.client_grp_value = this.dataFilter?.customer_name?.value;
    }
    if (filter.selectedClient) {
      filter.selectedClient = JSON.stringify(filter.selectedClient);
    }
    if (filter.selectedProject) {
      filter.selectedProject = JSON.stringify(filter.selectedProject);
    }

    const requestObject: SaveFilter = {
      query_string: this.serialize(filter),
      resource: 'managePLReport'
    };
    const dialogTitle = 'Save Filter Group';
    const dialogRef = this.layoutUtilsService.saveClientGroupName(dialogTitle, requestObject);
    dialogRef.afterClosed().subscribe((filtersResponse) => {
      if (filtersResponse) {
        // Remove query params, else the page will reload and if the filter was changed the query param will be replaced with the one in the query param.
        this.router.navigate([], {
          queryParams: {
            filterId: null
          },
          queryParamsHandling: 'merge'
        });
        this.getStoredFilters();
        this.layoutUtilsService.showActionNotification('Filter has been saved successfully', AlertType.Success);
      }
    });
  }
  // converts the object in to query param string
  serialize = (obj) => {
    const str = [];
    for (const p in obj) {
      if (p !== 'selectedClient' && p !== 'selectedProject') {
        if (obj.hasOwnProperty(p)) {
          str.push(encodeURIComponent(p) + '=' + encodeURIComponent(obj[p]));
        }
      } else {
        str.push(p + '=' + obj[p]);
      }
    }
    return str.join('&');
  };

  getStoredFilters() {
    const requestObject = {
      resource: 'managePLReport'
    };
    this.subscriptionManager.add(
      this.utilizationService.getStoredFilters(requestObject).subscribe((res: ISavedFilterList) => {
        this.sharedFilters = [];
        this.myFilters = [];
        this.filteredFilters = JSON.parse(JSON.stringify(res)); // to deep copy the object
        this.sharedFilters = this.filteredFilters?.data?.query_filters?.filter((q) => q.query_filter.is_shared === true);
        this.myFilters = this.filteredFilters?.data?.query_filters?.filter((q) => q.query_filter.is_shared === false);
        this.availableFilters = res.data.query_filters;
        this.cdf.detectChanges();
        this.routerListener();
      })
    );
  }

  monthlyValuesClick(rowData, monthlyData, pop, isNegative = false) {
    pop.open();
    this.loadingEmp = true;
    this.activeEmployee = [];
    const dateFormat = 'yyyy-MM-dd';
    let params = {
      start_date: this.datePipe.transform(new Date(monthlyData.key.slice(0, 4), monthlyData.key.slice(-2) - 1), dateFormat),
      end_date: this.datePipe.transform(new Date(monthlyData.key.slice(0, 4), monthlyData.key.slice(-2), 0), dateFormat)
    };
    this.subscriptionManager.add(
      this.utilizationService.getActiveEmployees(rowData.id, params).subscribe((res) => {
        if (res.body.data.positions.length > 0) {
          let pos = res.body.data.positions;
          let emp = [];
          pos.map(async (p) => {
            let obj = {
              customer: await this.getCustomerInfo(p.position.project.id),
              project: p.position.project,
              start_date: p.position.start_date,
              end_date: p.position.end_date,
              isNegative: isNegative
            };
            emp.push(obj);
          });
          this.activeEmployee = emp;
          this.loadingEmp = false;
        } else {
          this.loadingEmp = false;
        }
      })
    );
  }

  getCustomerInfo(projectId): Promise<Project> {
    return new Promise((resolve) => {
      if (projectId) {
        this.utilizationService.getProject(projectId).subscribe((res) => {
          resolve(res?.data);
        });
      } else {
        resolve(null);
      }
    });
  }

  closePopOver(p) {
    p.toggle();
  }

  closeAdjustModal() {
    this.adjustValueObj = null;
    this.showAdjustValuesDialog = false;
    this.isSubmitting = false;
  }

  showDialog(rowData, monthlyData) {
    this.adjustValueObj = null;
    this.showAdjustValuesDialog = true;
    this.loading$$.next(true);

    let title = '';
    let type = '';
    this.showAmtError = false;
    this.showNoteError = false;

    switch (rowData?.type) {
      case FINANCIAL_REVIEW_TYPES.PL_ADJUST:
        (title = 'Revenue Adjustment'), (type = 'Revenue Plug');
        break;
      case FINANCIAL_REVIEW_TYPES.COGS_ADJUST:
        (title = 'COGS Adjustment'), (type = 'Expense Plug');
        break;
      case FINANCIAL_REVIEW_TYPES.SGA_ADJUST:
        (title = 'SG&A'), (type = 'SG&A Plug');
        break;
    }
    let params = {
      month: monthlyData.key.slice(-2),
      year: monthlyData.key.slice(0, 4),
      type: type
    };
    this.subscriptionManager.add(
      this.utilizationService.getPNLPlugList(params).subscribe((res) => {
        this.adjustValueObj = {
          title: title,
          displayMonth: MONTH_NAMES[monthlyData.key.slice(-2) - 1] + ' ' + monthlyData.key.slice(0, 4),
          amount: monthlyData.value,
          month: monthlyData.key.slice(-2),
          year: monthlyData.key.slice(0, 4),
          type: type,
          reason: null,
          id: null
        };
        if (res?.body?.data?.pnl_plugs) {
          let resp = res?.body?.data?.pnl_plugs;
          this.adjustValueObj = {
            ...this.adjustValueObj,
            reason: resp[0].pnl_plug.reason,
            id: resp[0].pnl_plug.id
          };
        }
        this.loading$$.next(false);
      })
    );
  }

  saveAdjustValue() {
    let flag = true;
    if (this.adjustValueObj.amount === 0) {
    } else if (!this.adjustValueObj.amount) {
      flag = false;
      this.showAmtError = true;
    }
    if (!this.adjustValueObj?.reason?.trim()) {
      flag = false;
      this.showNoteError = true;
    }
    if (flag) {
      this.isSubmitting = true;
      const pnlObj = { ...this.adjustValueObj };
      delete pnlObj['title'];
      delete pnlObj['displayMonth'];

      if (pnlObj.id) {
        this.subscriptionManager.add(
          this.utilizationService.updatePNLPlug(pnlObj, pnlObj.id).subscribe((res) => {
            this.layoutUtilsService.showActionNotification(AppConstants.editPNLPlug, AlertType.Success);
            this.closeAdjustModal();
            this.getProjections();
          })
        );
      } else {
        this.subscriptionManager.add(
          this.utilizationService.createPNLPlug(pnlObj).subscribe((res) => {
            this.layoutUtilsService.showActionNotification(AppConstants.createPNLPlug, AlertType.Success);
            this.closeAdjustModal();
            this.getProjections();
          })
        );
      }
    }
  }

  // Multiple S&GA update methods
  openMultipleSgaDialog(): void {
    this.sgaPlugsLoading$.next(true);
    this.showMultipleSgaDialog = true;
    this.multipleSgaAmount = null;
    this.multipleSgaReason = '';
    this.getSgaPlugs();
  }

  closeMultipleSgaDialog(): void {
    this.showMultipleSgaDialog = false;
    this.showMultipleSgaError = false;
    this.showMultipleSgaAmountError = false;
    this.selectedSgaMonths = [];
    this.multipleSgaAmount = null;
    this.multipleSgaReason = '';
  }

  onSgaMonthSelectionChange(): void {
    this.showMultipleSgaError = false;
    this.showMultipleSgaAmountError = false;
  }

  multipleSgaValueChange(key: string): void {
    if (key === 'amount') {
      this.showMultipleSgaAmountError = false;
    }
    if (key === 'reason') {
      this.showMultipleSgaError = false;
    }
  }

  getSgaPlugs(): void {
    // Get available months from totals_monthly data
    if (this.projections?.totals_monthly) {
      this.sgaPlugs = this.projections.totals_monthly.map((total) => ({
        sga_plug: {
          id: `${total.monthly_total.year}${total.monthly_total.month.toString().padStart(2, '0')}`,
          month: total.monthly_total.month,
          year: total.monthly_total.year,
          amount: total.monthly_total.sga_plugs
        }
      }));

      this.sgaMonthOptions = this.sgaPlugs.map((plug) => {
        return {
          value: plug.sga_plug.id,
          label: `${this.getMonthName(plug.sga_plug.month)} ${plug.sga_plug.year}`,
          data: plug.sga_plug,
          sortValue: plug.sga_plug.year * 100 + plug.sga_plug.month
        };
      });

      this.sgaMonthOptions.sort((a, b) => b.sortValue - a.sortValue);
      this.selectedSgaMonths = [];
      this.multipleSgaAmount = null;
      this.multipleSgaReason = '';
    } else {
      this.sgaPlugs = [];
      this.sgaMonthOptions = [];
    }
    this.sgaPlugsLoading$.next(false);
    this.cdf.detectChanges();
  }

  getMonthName(monthNumber: number): string {
    return MONTH_NAMES[monthNumber - 1];
  }

  saveMultipleSgaPlugs(): void {
    if (this.selectedSgaMonths?.length === 0) {
      return;
    }

    let isValid = true;

    if (!this.multipleSgaAmount && this.multipleSgaAmount !== 0) {
      this.showMultipleSgaAmountError = true;
      isValid = false;
    }

    if (!this.multipleSgaReason) {
      this.showMultipleSgaError = true;
      isValid = false;
    }

    if (!isValid) {
      return;
    }

    this.isMultipleSgaSubmitting = true;
    const getPlugObservables = this.selectedSgaMonths.map((month) => {
      const plugData = {
        year: month.data.year,
        month: month.data.month,
        type: 'SG&A Plug'
      };

      return this.utilizationService.getPNLPlugList(plugData);
    });

    forkJoin(getPlugObservables)
      .pipe(
        switchMap((responses) => {
          const updateOrCreateObservables = responses.map((response, index) => {
            const month = this.selectedSgaMonths[index];

            const plugData = {
              amount: this.multipleSgaAmount,
              reason: this.multipleSgaReason,
              month: month.data.month,
              year: month.data.year,
              type: 'SG&A Plug'
            };

            const existingPlug = response && response?.body;

            if (existingPlug && existingPlug?.data?.pnl_plugs?.length && existingPlug?.data?.pnl_plugs[0]?.pnl_plug?.id) {
              return this.utilizationService.updatePNLPlug({ ...plugData }, existingPlug?.data?.pnl_plugs[0]?.pnl_plug?.id);
            } else {
              return this.utilizationService.createPNLPlug({ ...plugData, id: null });
            }
          });
          return forkJoin(updateOrCreateObservables);
        })
      )
      .subscribe(
        (finalResponses) => {
          this.layoutUtilsService.showActionNotification(
            `${AppConstants.sgaPlugSuccessMessage} ${this.selectedSgaMonths.length} ${AppConstants.sgaPlugSuccessMessageSuffix}`,
            AlertType.Success
          );
          this.isMultipleSgaSubmitting = false;
          this.showMultipleSgaDialog = false;
          this.getProjections();
          this.cdf.detectChanges();
        },
        (error) => {
          this.layoutUtilsService.showActionNotification(error?.error?.message || AppConstants.sgaPlugErrorMessage, AlertType.Error);
          this.isMultipleSgaSubmitting = false;
          this.cdf.detectChanges();
        }
      );
  }

  valueChange(key) {
    if (key === 'amount') {
      this.showAmtError = false;
    }
    if (key === 'reason') {
      this.showNoteError = false;
    }
  }

  searchFilters(filter: string) {
    this.filteredFilters.data.query_filters = filter.length
      ? this.availableFilters.filter((availableFilter) => availableFilter.query_filter.name.toLowerCase().includes(filter.toLowerCase()))
      : this.availableFilters;
    this.sharedFilters = this.filteredFilters?.data?.query_filters.filter((q) => q.query_filter.is_shared === true);
    this.myFilters = this.filteredFilters?.data?.query_filters.filter((q) => q.query_filter.is_shared === false);
  }

  editFilter(filter) {
    this.showEditDialog = true;
    this.editFilterObj = _.cloneDeep(filter);
  }
  shareFilter(filterOption) {
    this.showShareDialog = true;
    this.shareFilterObj = { ...filterOption, header: 'Share', text: `Do you want to share Filter "${filterOption.query_filter.name}" to all users publically?` };
    this.shareFilterObj.query_filter.is_shared = !this.shareFilterObj.query_filter.is_shared;
  }

  unShareFilter(filterOption) {
    this.showShareDialog = true;
    this.shareFilterObj = { ...filterOption, header: 'Unshare', text: `Do you want to unshare Filter "${filterOption.query_filter.name}" from all users?` };
    this.shareFilterObj.query_filter.is_shared = !this.shareFilterObj.query_filter.is_shared;
  }

  saveShareFilter() {
    this.isSubmitting = true;
    this.subscriptionManager.add(
      this.utilizationService.updateFilter(this.shareFilterObj.query_filter.id, this.shareFilterObj).subscribe(
        (res) => {
          this.layoutUtilsService.showActionNotification(this.shareFilterObj.query_filter.is_shared ? AppConstants.shareFilter : AppConstants.unShareFilter, AlertType.Success);
          this.isSubmitting = false;
          this.closeModal();
          this.getStoredFilters();
          this.showSavedFilter = true;
          this.showFilterListDialog = true;
          this.utilizationService.showNewSharedFilter.next('Manage P&L');
          this.cdf.detectChanges();
        },
        () => (this.isSubmitting = false)
      )
    );
  }
  inputFilterName() {
    this.showNameError = false;
  }

  deleteFilter(filterOption) {
    this.showDeleteDialog = true;
    this.deleteFilterObj = filterOption;
  }

  saveDeleteFilter() {
    this.isSubmitting = true;
    this.subscriptionManager.add(
      this.utilizationService.deleteStoredFilters(this.deleteFilterObj.query_filter.id).subscribe(
        (res) => {
          this.layoutUtilsService.showActionNotification(AppConstants.deleteFilter, AlertType.Success);
          this.isSubmitting = false;
          this.closeModal();
          this.getStoredFilters();
          this.showSavedFilter = true;
          this.showFilterListDialog = true;
          this.utilizationService.showNewSharedFilter.next('Manage P&L');
          this.cdf.detectChanges();
        },
        () => (this.isSubmitting = false)
      )
    );
  }

  saveEditFilter() {
    if (!this.editFilterObj.query_filter.name.length) {
      this.showNameError = true;
    } else {
      this.isSubmitting = true;
      this.subscriptionManager.add(
        this.utilizationService.updateFilter(this.editFilterObj.query_filter.id, this.editFilterObj).subscribe(
          (res) => {
            this.layoutUtilsService.showActionNotification(AppConstants.updateFilter, AlertType.Success);
            this.isSubmitting = false;
            this.closeModal();
            this.getStoredFilters();
            this.showSavedFilter = true;
            this.showFilterListDialog = true;
            this.cdf.detectChanges();
          },
          () => (this.isSubmitting = false)
        )
      );
    }
  }

  private routerListener() {
    this.activatedRoute.queryParamMap.subscribe((params: Params) => {
      // grab the filter id from the url if present we will make an api call to get the specific filter from that id.
      if (params.params.filterId) {
        this.queryFilterId = params.params.filterId;
        this.getTheFilterById();
      }
    });
  }

  // used to get the filter by its id
  getTheFilterById() {
    this.subscriptionManager.add(
      this.projectService.getTheFilterById(this.queryFilterId).subscribe(
        (res) => {
          // if we get some response only then we may try to apply filter
          if (res) {
            this.selectedFilter = res?.data;
            const filterValue = this.sharedFilters?.find((f) => JSON.stringify(f) === JSON.stringify(this.selectedFilter));
            if (filterValue) {
              this.selectedFilterFormControl.setValue(filterValue);
            }
            this.applyFilter();
          }
        },
        (error) => {
          this.layoutUtilsService.showActionNotification(AppConstants.problemFetchingFilterById, AlertType.Error);
        }
      )
    );
  }

  copyLinkToTheFilter(filterId: number) {
    const filterHolder = document.createElement('textarea');
    filterHolder.style.position = 'fixed';
    filterHolder.style.left = '0';
    filterHolder.style.top = '0';
    filterHolder.style.opacity = '0';
    // construct the full url to be copied
    // e.g. http://localhost:4200/project/manage?filterId=3
    const hostName = `${window.location.protocol}${window.location.host}`;
    const filterString = `${hostName}${this.router.url.split('?')[0]}/?filterId=${filterId}`;

    filterHolder.value = filterString;
    document.body.appendChild(filterHolder);
    filterHolder.focus();
    filterHolder.select();
    document.execCommand('copy');
    document.body.removeChild(filterHolder);
    this.layoutUtilsService.showActionNotification(AppConstants.filterLinkCopied, AlertType.Success);
  }

  applySelectedFilterAndUpdateUrl() {
    this.showSavedFilter = false;
    this.selectedFilter = this.selectedFilterFormControl.value;
    this.router.navigate([], { relativeTo: this.activatedRoute, queryParams: { filterId: this.selectedFilter?.query_filter?.id }, queryParamsHandling: 'merge' });
  }

  getIsBenchNegative(type, monthlyDataValue) {
    return type === 'Bench' && monthlyDataValue < 0;
  }

  showBenchToolTip(): string {
    return `Note: Bench is negative because this person's billable hours exceed their expected utilization for this month.`;
  }

  private dialogResolve: (value: boolean) => void;

  getPausedProjectList(projectIds = null): Promise<boolean> {
    let query = { validations: 'NO,PAUSED' };
    if (projectIds) {
      query['project_ids'] = projectIds;
    }
    return new Promise<boolean>((resolve) => {
      this.subscriptionManager.add(
        this.adminService.getPausedProjectList(query).subscribe(
          (res: ProjectList) => {
            this.loading$.next(false);
            if (res?.data?.projects?.length) {
              this.pausedProjectList = res.data.projects;
              this.showPausedProjectDialog = true;
              this.dialogResolve = resolve;
            } else {
              resolve(true);
            }
          },
          () => {
            this.loading$.next(false);
            resolve(true);
          }
        )
      );
    });
  }

  continue(): void {
    this.showPausedProjectDialog = false;
    if (this.dialogResolve) {
      this.dialogResolve(true);
      this.dialogResolve = null;
    }
  }

  async wait(): Promise<void> {
    this.isResumeValidationInProgress = true;
    const validationPromises = this.pausedProjectList.map(
      (project) =>
        new Promise<void>(async (resolve) => {
          this.calculatingProjectName = project?.project?.name;
          this.startRotatingMessages();
          this.startRotatingProjectName();
          this.resumeProjectValidation(project.project.id);
          const projectObj = await this.checkValidationStatus(project.project.id);

          if (projectObj?.validated === 'YES') {
            resolve();
          } else {
            const intervalId = setInterval(async () => {
              const updatedProjectObj = await this.checkValidationStatus(project.project.id);
              if (updatedProjectObj?.validated === 'YES') {
                clearInterval(intervalId);
                resolve();
              }
            }, 4000);
          }
        })
    );

    Promise.all(validationPromises).then(() => {
      if (this.dialogResolve) {
        this.isResumeValidationInProgress = false;
        this.showPausedProjectDialog = false;
        this.dialogResolve(true);
        this.dialogResolve = null;
      }
    });
  }

  resumeProjectValidation(projectId: number): void {
    this.subscriptionManager.add(
      this.projectService.resumeProjectValidation(projectId).subscribe({
        complete: () => {
          //TODO: Handle completion if needed
        }
      })
    );
  }

  checkValidationStatus(id: number): Promise<any> {
    return new Promise((resolve, reject) => {
      if (id) {
        this.subscriptionManager.add(
          this.projectService.getValidationStatus(id).subscribe({
            next: (res) => {
              resolve(res.data.project);
            },
            error: (err) => {
              reject(err);
            }
          })
        );
      } else {
        reject();
      }
    });
  }

  private startRotatingMessages(): void {
    this.currentMessageIndex = 0;
    this.updateCurrentMessage();

    if (this.messageInterval) {
      this.messageInterval.unsubscribe();
    }

    this.messageInterval = interval(2000).subscribe(() => {
      this.currentMessageIndex = (this.currentMessageIndex + 1) % AppConstants.RECALCULATION_MESSAGES.length;
      this.updateCurrentMessage();
    });
  }

  private updateCurrentMessage(): void {
    this.rotatingMessage = AppConstants.RECALCULATION_MESSAGES[this.currentMessageIndex];
  }

  private startRotatingProjectName(): void {
    let projectIndex = 0;
    const projectNameInterval = interval(10000).subscribe(() => {
      projectIndex = (projectIndex + 1) % this.pausedProjectList.length;
      this.calculatingProjectName = this.pausedProjectList[projectIndex]?.project?.name;
    });
  }

  isTypeWithDirectDisplay(rowNode: any): boolean {
    const type = rowNode?.node?.data?.type;
    return type === 'SG&A' || type === 'Revenue Adjustment' || type === 'COGS Adjustment';
  }

  getSeparateExpense(rowData: any, monthlyExpense: any): string {
    let matchingPosition = [];
    this.projections?.projects?.find((project) => {
      return project?.project?.projection?.valid_monthly_projections?.some(({ valid_monthly_projection }) => {
        const key = `${valid_monthly_projection.year}${String(valid_monthly_projection.month).padStart(2, '0')}`;
        if (key !== monthlyExpense.key) return false;

        const match = valid_monthly_projection.validated_monthly_positions.find(({ validated_monthly_position: pos }) => {
          const emp = pos.position?.employee;
          const isMatched = pos.position?.name === rowData.type && emp && `${emp.first_name} ${emp.last_name}`.toLowerCase() === rowData.name.toLowerCase();

          if (isMatched) {
            matchingPosition.push(pos);
            return true;
          }

          return false;
        });

        return !!match;
      });
    });

    return `Direct Cost : ${this.addCommasToNumbersPipe.transform(matchingPosition[0]?.direct_cost) || 0.0}<br>
              Contract Cost : ${this.addCommasToNumbersPipe.transform(matchingPosition[0]?.contract_cost) || 0.0}<br>
              Open Positions Cost : ${this.addCommasToNumbersPipe.transform(matchingPosition[0]?.open_positions_cost) || 0.0}<br>
              Expenses : ${this.addCommasToNumbersPipe.transform(matchingPosition[0]?.expenses) || 0.0}`;
  }
}
